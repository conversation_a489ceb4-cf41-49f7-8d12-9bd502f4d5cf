import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  useColorModeValue,
} from '@chakra-ui/react';
import { FaInbox } from 'react-icons/fa';
import Inbox from './components/Inbox';
import ContentPageBox from './components/ContentPageBox';

const InboxPage: React.FC = () => {
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');

  return (
    <ContentPageBox>
      <Container maxW="95%" py={8}>
        <VStack spacing={6} align="stretch">
          {/* Page Header */}
          <VStack spacing={3} align="start">
            <Heading
              size="lg"
              color={textColor}
              display="flex"
              alignItems="center"
              gap={3}
            >
              <FaInbox />
              Inbox
            </Heading>
            <Text color={mutedColor} fontSize="md">
              Stay updated with welcome messages, feature announcements, feedback responses, and important notifications.
            </Text>
          </VStack>

          {/* Inbox Component */}
          <Box h="calc(100vh - 250px)" minH="600px">
            <Inbox />
          </Box>
        </VStack>
      </Container>
    </ContentPageBox>
  );
};

export default InboxPage;
