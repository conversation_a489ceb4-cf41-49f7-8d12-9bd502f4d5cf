import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  VStack,
  HStack,
  Text,
  Badge,
  Button,
  IconButton,
  Flex,
  useColorModeValue,
  useToast,
  Spinner,
  Divider,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useBreakpointValue,
  Card,
  CardBody,
  Avatar,
  Tooltip,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import {
  FaInbox,
  FaStar,
  FaArchive,
  FaTrash,
  FaEllipsisV,
  FaEnvelope,
  FaEnvelopeOpen,
  FaFilter,
  FaExternalLinkAlt,
  FaBell,
  FaGift,
  FaUser,
  FaCog,
  FaReply,
} from 'react-icons/fa';
import { useQuery, useAction } from 'wasp/client/operations';
import {
  getInboxMessages,
  getInboxStats,
  markMessageAsRead,
  markMessageAsStarred,
  archiveMessage,
  deleteMessage,
} from 'wasp/client/operations';
import { motion } from 'framer-motion';

const MotionBox = motion(Box);

interface InboxProps {
  onClose?: () => void;
}

const Inbox: React.FC<InboxProps> = ({ onClose }) => {
  const [filter, setFilter] = useState<'all' | 'unread' | 'starred' | 'archived'>('all');
  const [selectedMessage, setSelectedMessage] = useState<any>(null);
  
  const toast = useToast();
  
  // Theme colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');
  const unreadBg = useColorModeValue('blue.50', 'blue.900');
  
  // Responsive layout
  const isMobile = useBreakpointValue({ base: true, md: false });
  
  // Queries and actions
  const { data: messages, isLoading: messagesLoading, refetch: refetchMessages } = useQuery(getInboxMessages, { filter });
  const { data: stats, refetch: refetchStats } = useQuery(getInboxStats);
  
  const markAsReadAction = useAction(markMessageAsRead);
  const markAsStarredAction = useAction(markMessageAsStarred);
  const archiveMessageAction = useAction(archiveMessage);
  const deleteMessageAction = useAction(deleteMessage);

  // Message type icons and colors
  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'welcome': return FaUser;
      case 'promotion': return FaGift;
      case 'feedback_response': return FaReply;
      case 'system': return FaCog;
      case 'email_copy': return FaEnvelope;
      case 'broadcast': return FaBell;
      default: return FaInbox;
    }
  };

  const getMessageTypeColor = (type: string) => {
    switch (type) {
      case 'welcome': return 'green';
      case 'promotion': return 'purple';
      case 'feedback_response': return 'blue';
      case 'system': return 'orange';
      case 'email_copy': return 'gray';
      case 'broadcast': return 'red';
      default: return 'blue';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'red';
      case 'high': return 'orange';
      case 'normal': return 'blue';
      case 'low': return 'gray';
      default: return 'blue';
    }
  };

  // Handle message actions
  const handleMarkAsRead = async (messageId: string) => {
    try {
      await markAsReadAction({ messageId });
      refetchMessages();
      refetchStats();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to mark message as read',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleToggleStar = async (messageId: string, currentStarred: boolean) => {
    try {
      await markAsStarredAction({ messageId, starred: !currentStarred });
      refetchMessages();
      refetchStats();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update message',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleArchive = async (messageId: string) => {
    try {
      await archiveMessageAction({ messageId });
      refetchMessages();
      refetchStats();
      setSelectedMessage(null);
      toast({
        title: 'Message archived',
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to archive message',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleDelete = async (messageId: string) => {
    try {
      await deleteMessageAction({ messageId });
      refetchMessages();
      refetchStats();
      setSelectedMessage(null);
      toast({
        title: 'Message deleted',
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete message',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleMessageClick = async (message: any) => {
    setSelectedMessage(message);
    if (!message.isRead) {
      await handleMarkAsRead(message.id);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  if (messagesLoading) {
    return (
      <Flex justify="center" align="center" h="400px">
        <Spinner size="lg" color="blue.500" />
      </Flex>
    );
  }

  return (
    <Box h="100%" bg={bgColor} borderRadius="lg" border="1px" borderColor={borderColor}>
      {/* Header */}
      <Flex
        p={4}
        borderBottom="1px"
        borderColor={borderColor}
        justify="space-between"
        align="center"
      >
        <HStack spacing={3}>
          <FaInbox color="blue" />
          <Text fontSize="lg" fontWeight="600" color={textColor}>
            Inbox
          </Text>
          {stats && stats.unread > 0 && (
            <Badge colorScheme="red" borderRadius="full">
              {stats.unread}
            </Badge>
          )}
        </HStack>
        
        {/* Filter Menu */}
        <Menu>
          <MenuButton as={IconButton} icon={<FaFilter />} variant="ghost" size="sm" />
          <MenuList>
            <MenuItem onClick={() => setFilter('all')}>
              All Messages {stats && `(${stats.total})`}
            </MenuItem>
            <MenuItem onClick={() => setFilter('unread')}>
              Unread {stats && `(${stats.unread})`}
            </MenuItem>
            <MenuItem onClick={() => setFilter('starred')}>
              Starred {stats && `(${stats.starred})`}
            </MenuItem>
            <MenuItem onClick={() => setFilter('archived')}>
              Archived {stats && `(${stats.archived})`}
            </MenuItem>
          </MenuList>
        </Menu>
      </Flex>

      {/* Messages List */}
      <Box h="calc(100% - 80px)" overflowY="auto">
        {!messages || messages.length === 0 ? (
          <Flex direction="column" align="center" justify="center" h="100%" p={8}>
            <FaInbox size={48} color={mutedColor} />
            <Text mt={4} color={mutedColor} textAlign="center">
              {filter === 'all' ? 'No messages yet' : `No ${filter} messages`}
            </Text>
            <Text fontSize="sm" color={mutedColor} textAlign="center" mt={2}>
              Welcome messages, updates, and notifications will appear here
            </Text>
          </Flex>
        ) : (
          <VStack spacing={0} align="stretch">
            {messages.map((message: any, index: number) => {
              const TypeIcon = getMessageTypeIcon(message.type);
              const isUnread = !message.isRead;
              
              return (
                <MotionBox
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <Box
                    p={4}
                    borderBottom="1px"
                    borderColor={borderColor}
                    bg={isUnread ? unreadBg : 'transparent'}
                    _hover={{ bg: hoverBg }}
                    cursor="pointer"
                    onClick={() => handleMessageClick(message)}
                  >
                    <HStack spacing={3} align="start">
                      {/* Sender Avatar */}
                      <Avatar
                        size="sm"
                        name={message.senderName}
                        src={message.senderAvatar}
                        bg={`${getMessageTypeColor(message.type)}.500`}
                      />
                      
                      {/* Message Content */}
                      <VStack flex={1} align="start" spacing={1}>
                        <HStack justify="space-between" w="100%">
                          <HStack spacing={2}>
                            <Text
                              fontSize="sm"
                              fontWeight={isUnread ? "600" : "500"}
                              color={textColor}
                            >
                              {message.senderName}
                            </Text>
                            <Badge
                              size="sm"
                              colorScheme={getMessageTypeColor(message.type)}
                              variant="subtle"
                            >
                              <TypeIcon size={10} style={{ marginRight: 4 }} />
                              {message.type.replace('_', ' ')}
                            </Badge>
                            {message.priority !== 'normal' && (
                              <Badge
                                size="sm"
                                colorScheme={getPriorityColor(message.priority)}
                                variant="outline"
                              >
                                {message.priority}
                              </Badge>
                            )}
                          </HStack>
                          <Text fontSize="xs" color={mutedColor}>
                            {formatDate(message.createdAt)}
                          </Text>
                        </HStack>
                        
                        <Text
                          fontSize="sm"
                          fontWeight={isUnread ? "600" : "400"}
                          color={textColor}
                          noOfLines={1}
                        >
                          {message.title}
                        </Text>
                        
                        <Text
                          fontSize="xs"
                          color={mutedColor}
                          noOfLines={2}
                        >
                          {message.content.replace(/<[^>]*>/g, '')} {/* Strip HTML */}
                        </Text>
                      </VStack>
                      
                      {/* Action Buttons */}
                      <VStack spacing={1}>
                        <Tooltip label={message.isStarred ? "Unstar" : "Star"}>
                          <IconButton
                            icon={<FaStar />}
                            size="xs"
                            variant="ghost"
                            color={message.isStarred ? "yellow.500" : "gray.400"}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleToggleStar(message.id, message.isStarred);
                            }}
                            aria-label={message.isStarred ? "Unstar message" : "Star message"}
                          />
                        </Tooltip>
                        
                        {isUnread && (
                          <Box w={2} h={2} bg="blue.500" borderRadius="full" />
                        )}
                      </VStack>
                    </HStack>
                  </Box>
                </MotionBox>
              );
            })}
          </VStack>
        )}
      </Box>
    </Box>
  );
};

export default Inbox;
