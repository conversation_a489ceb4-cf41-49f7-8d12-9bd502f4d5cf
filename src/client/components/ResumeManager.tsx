import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Button,
  FormControl,
  Heading,
  HStack,
  Input,
  Text,
  VStack,
  useToast,
  Spinner,
  List,
  ListItem,
  Grid,
  GridItem,
  Tooltip,
  useColorModeValue,
  IconButton,
  Select,
  Flex,
  Badge,
  useBreakpointValue,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,

} from '@chakra-ui/react';
import { DownloadIcon, CloseIcon } from '@chakra-ui/icons';
import { FaTrash } from 'react-icons/fa';
import { FaFileAlt, FaFilePdf, FaFileWord, FaFileImage, FaUpload, FaPlus, FaPrint, FaChevronLeft, FaChevronRight, FaEye, FaEdit, FaCheck, FaTimes } from 'react-icons/fa';
import { type Resume, type ResumeTemplate } from '../../shared/types';
import { getUserResumes, createResume, deleteResume, updateResume, parsePDFContent } from 'wasp/client/operations';
import { useQuery } from 'wasp/client/operations';
import { useAuth } from 'wasp/client/auth';
import ActionButton from '../components/ActionButton';
import ContentContainer from '../components/ContentContainer';
import * as pdfjsLib from 'pdfjs-dist';
import ResumeForm from './ResumeForm';
import { EmptyResumes } from './EmptyState';
import { generateResumePDF, printResume, saveResumeAsPDF } from '../utils/pdfGenerator';
import { SimpleLoading } from './LoadingState';
import { GeneralError } from './ErrorState';
import ResumeTemplateGallery from './ResumeTemplateGallery';
import { getTemplateById } from '../data/resumeTemplates';
import { useTemplateManager } from '../utils/templateManager';
import Pagination from './Pagination';

type CreationStep = 'template' | 'form';

interface ResumeManagerProps {
  onFileUpload?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onCreateResume?: () => void;
  onCancelCreate?: () => void;
  isCreatingResume?: boolean;
  fileInputRef?: React.RefObject<HTMLInputElement>;
  selectedTemplateFromParent?: ResumeTemplate | null;
  onResumeCreated?: () => void;
}

const ResumeManager: React.FC<ResumeManagerProps> = ({
  onFileUpload: externalOnFileUpload,
  onCreateResume: externalOnCreateResume,
  onCancelCreate: externalOnCancelCreate,
  isCreatingResume: externalIsCreatingResume,
  fileInputRef: externalFileInputRef,
  selectedTemplateFromParent,
  onResumeCreated
}) => {
  const [selectedResume, setSelectedResume] = useState<Resume | null>(null);
  const [isPdfReady, setIsPdfReady] = useState<boolean>(false);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [fileType, setFileType] = useState<string | null>(null);
  const [pdfScale, setPdfScale] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [internalIsCreatingResume, setInternalIsCreatingResume] = useState(false);
  const [creationStep, setCreationStep] = useState<CreationStep>('template');
  const [selectedTemplate, setSelectedTemplate] = useState<ResumeTemplate | null>(null);
  const [resumeCurrentPage, setResumeCurrentPage] = useState(1);
  const [resumeItemsPerPage, setResumeItemsPerPage] = useState(10);
  const internalFileInputRef = useRef<HTMLInputElement>(null);

  // Modal state for resume preview
  const { isOpen: isViewModalOpen, onOpen: onViewModalOpen, onClose: onViewModalClose } = useDisclosure();

  // Editable title state
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [editableTitle, setEditableTitle] = useState('');

  // Initialize editable title when selectedResume changes
  React.useEffect(() => {
    if (selectedResume) {
      setEditableTitle(selectedResume.title || '');
    }
  }, [selectedResume]);

  // Handle template selection from parent (Templates tab)
  React.useEffect(() => {
    if (selectedTemplateFromParent && externalIsCreatingResume) {
      setSelectedTemplate(selectedTemplateFromParent);
      setCreationStep('form'); // Skip template selection and go directly to form
    }
  }, [selectedTemplateFromParent, externalIsCreatingResume]);

  // Handle title editing
  const handleTitleEdit = () => {
    setIsEditingTitle(true);
  };

  const handleTitleSave = async () => {
    if (selectedResume && editableTitle.trim()) {
      try {
        const updatedResume = await updateResume({
          id: selectedResume.id,
          data: { title: editableTitle.trim() }
        });

        // Update local state with the updated resume
        setSelectedResume(updatedResume);
        setIsEditingTitle(false);

        // Refetch resumes to ensure consistency
        refetch();

        toast({
          title: 'Title updated successfully',
          status: 'success',
          duration: 2000,
        });
      } catch (error) {
        console.error('Error updating resume title:', error);
        toast({
          title: 'Failed to update title',
          status: 'error',
          duration: 3000,
        });
      }
    }
  };

  const handleTitleCancel = () => {
    setEditableTitle(selectedResume?.title || '');
    setIsEditingTitle(false);
  };

  // Use external props if provided, otherwise use internal state
  // However, if we're editing a template (selectedResume exists and no fileData), prioritize internal state
  const isCreatingResume = (selectedResume && !selectedResume.fileData && internalIsCreatingResume)
    ? internalIsCreatingResume
    : (externalIsCreatingResume !== undefined ? externalIsCreatingResume : internalIsCreatingResume);
  const fileInputRef = externalFileInputRef || internalFileInputRef;
  const toast = useToast();
  const pdfContainerRef = useRef<HTMLDivElement>(null);
  const { data: user } = useAuth();

  // Use template manager for proper template isolation
  const templateManager = useTemplateManager();

  // Fetch user's resumes only if user is authenticated
  const { data: resumes, isLoading, error, refetch } = useQuery(getUserResumes, {}, { enabled: !!user });

  // Responsive values - moved to top to avoid conditional hook calls
  const buttonSize = useBreakpointValue({ base: 'sm', md: 'md' });
  const spacing = useBreakpointValue({ base: 3, md: 4 });

  // Color mode values - moved to top to avoid conditional hook calls
  const topSectionBg = useColorModeValue('gray.50', 'gray.800');
  const topSectionBorderColor = useColorModeValue('gray.200', 'gray.600');
  const selectLabelColor = useColorModeValue('gray.700', 'gray.300');
  const selectBg = useColorModeValue('white', 'gray.700');
  const selectBorderColor = useColorModeValue('gray.300', 'gray.600');
  const containerBorderColor = useColorModeValue('gray.200', 'gray.600');
  const templateSelectedBg = useColorModeValue('purple.50', 'purple.900');
  const templateSelectedBorderColor = useColorModeValue('purple.200', 'purple.600');

  // Select the first resume by default when data is loaded
  useEffect(() => {
    if (resumes && resumes.length > 0 && !selectedResume) {
      handleSelectResume(resumes[0]);
    }
  }, [resumes, selectedResume]);

  const handleFileButtonClick = () => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to upload resumes.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'application/pdf':
        return <FaFilePdf />;
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return <FaFileWord />;
      case 'image/jpeg':
      case 'image/png':
      case 'image/gif':
        return <FaFileImage />;
      default:
        return <FaFileAlt />;
    }
  };

  const handleSelectResume = async (resume: Resume) => {
    setSelectedResume(resume);

    // Use template manager to get the correct template for this resume
    const template = templateManager.getTemplateForResume(resume.id, resume.templateId);
    setSelectedTemplate(template);

    // If the resume has file data, create a blob and URL for preview
    if (resume.fileData) {
      try {
        const fileData = resume.fileData as any;
        if (fileData.data && fileData.type) {
          // For base64 data that includes the data URL prefix
          let base64Data = fileData.data;
          if (base64Data.includes(',')) {
            base64Data = base64Data.split(',')[1];
          }

          const byteCharacters = atob(base64Data);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);
          const blob = new Blob([byteArray], { type: fileData.type });
          const fileUrl = URL.createObjectURL(blob);
          setPdfUrl(fileUrl);
          setFileType(fileData.type);
          setIsPdfReady(true);
          setCurrentPage(1);

          // If it's a PDF, try to get the total pages
          if (fileData.type.includes('pdf')) {
            try {
              // Ensure pdfjsLib is loaded. This might need a dynamic import or being loaded outside.
              // For now, assuming it's available via static import.
              pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
              const loadingTask = pdfjsLib.getDocument({ data: Uint8Array.from(atob(base64Data), c => c.charCodeAt(0)) });
              loadingTask.promise.then((pdf: any) => {
                setTotalPages(pdf.numPages);
              }).catch((err: any) => {
                console.error('Error loading PDF:', err);
                setTotalPages(1);
              });
            } catch (err) {
              console.error('Error with PDF.js:', err);
              setTotalPages(1);
            }
          } else {
            setTotalPages(1);
          }
        }
      } catch (error) {
        console.error('Error creating file preview:', error);
        setPdfUrl(null);
        setIsPdfReady(false);
      }
    } else {
      // Handle resumes without file data (e.g., manually created ones)
      // Try to generate a PDF preview from the resume data
      await generatePreviewForCreatedResume(resume);
    }
  };

  const generatePreviewForCreatedResume = async (resume: Resume) => {
    try {
      // Use template manager to get the correct template for this resume
      const template = templateManager.getTemplateForResume(resume.id, resume.templateId);

      // Generate PDF preview
      const pdfUrl = await generateResumePDF(resume, template || undefined);

      if (pdfUrl) {
        setPdfUrl(pdfUrl);
        setFileType('text/html'); // Since we're generating HTML-based preview
        setIsPdfReady(true);
        setCurrentPage(1);
        setTotalPages(1);
      } else {
        // Fallback to no preview
        setPdfUrl(null);
        setIsPdfReady(false);
        setFileType(null);
        setTotalPages(1);
        setCurrentPage(1);
      }
    } catch (error) {
      console.error('Error generating resume preview:', error);
      setPdfUrl(null);
      setIsPdfReady(false);
      setFileType(null);
      setTotalPages(1);
      setCurrentPage(1);
    }
  };

  const generatePreviewForCreatedResumeWithTemplate = async (resume: Resume, template: ResumeTemplate) => {
    try {
      // Clean up previous PDF URL to force refresh
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }

      // Generate PDF preview with the specific template
      const newPdfUrl = await generateResumePDF(resume, template);

      if (newPdfUrl) {
        setPdfUrl(newPdfUrl);
        setFileType('text/html'); // Since we're generating HTML-based preview
        setIsPdfReady(true);
        setCurrentPage(1);
        setTotalPages(1);
      } else {
        // Fallback to no preview
        setPdfUrl(null);
        setIsPdfReady(false);
        setFileType(null);
        setTotalPages(1);
        setCurrentPage(1);
      }
    } catch (error) {
      console.error('Error generating resume preview with template:', error);
      setPdfUrl(null);
      setIsPdfReady(false);
      setFileType(null);
      setTotalPages(1);
      setCurrentPage(1);
    }
  };

  // Action handlers for newly created resumes
  const handlePrintResume = async () => {
    if (!selectedResume) return;

    try {
      const template = templateManager.getTemplateForResume(selectedResume.id, selectedResume.templateId);
      await printResume(selectedResume, template || undefined);
      toast({
        title: 'Printing resume',
        description: 'Resume sent to printer successfully.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Print failed',
        description: 'Unable to print resume. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleSaveAsPDF = async () => {
    if (!selectedResume) return;

    try {
      const template = templateManager.getTemplateForResume(selectedResume.id, selectedResume.templateId);
      await saveResumeAsPDF(selectedResume, template || undefined);
      toast({
        title: 'Resume saved',
        description: 'Resume downloaded successfully.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Save failed',
        description: 'Unable to save resume. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Helper function to extract text from PDF
  const extractPDFText = async (file: File): Promise<string> => {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
      const loadingTask = pdfjsLib.getDocument(uint8Array);
      const pdf = await loadingTask.promise;

      let textContent = '';
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const content = await page.getTextContent();
        const text = content.items
          .map((item: any) => item.str || '')
          .join(' ');
        textContent += text + '\n';
      }

      return textContent.trim();
    } catch (error) {
      console.error('Error extracting PDF text:', error);
      throw error;
    }
  };

  const handleEditTemplate = () => {
    if (!selectedResume) return;

    // Set the resume for editing and go to template selection
    setInternalIsCreatingResume(true);
    setCreationStep('template');
    // Keep the current template selected if available
    // setSelectedTemplate(null); // Don't clear the current template
  };

  const handleTemplateChange = async (newTemplate: ResumeTemplate, showToast: boolean = true) => {
    if (!selectedResume) return;

    try {
      // Update the resume in the database with the new template
      const updatedResume = await updateResume({
        id: selectedResume.id,
        data: {
          templateId: newTemplate.id
        }
      });

      // Use template manager to set the template for this specific resume
      templateManager.setTemplateForResume(selectedResume.id, newTemplate.id);

      // Update local state
      setSelectedTemplate(newTemplate);

      // Update the selected resume with the new template ID
      setSelectedResume(prev => prev ? { ...prev, templateId: newTemplate.id } : null);

      // Regenerate preview with new template - pass the new template directly
      await generatePreviewForCreatedResumeWithTemplate(selectedResume, newTemplate);

      // Exit template selection mode
      setInternalIsCreatingResume(false);
      setCreationStep('template');

      // Refetch resumes to ensure consistency
      refetch();

      // Only show toast if requested (to avoid duplicate notifications)
      if (showToast) {
        toast({
          title: 'Template updated',
          description: 'Resume template has been changed successfully.',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error('Error updating template:', error);
      // Always show error toast
      toast({
        title: 'Update failed',
        description: 'Unable to change template. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  async function internalOnFileUpload(event: React.ChangeEvent<HTMLInputElement>) {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to upload resumes.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (event.target.files == null || event.target.files.length === 0) return;

    const file = event.target.files[0];
    setFileType(file.type);

    // Create a URL for the file preview (for PDFs and images)
    const fileUrl = URL.createObjectURL(file);
    setPdfUrl(fileUrl);
    setIsPdfReady(true);

    // Read file as base64 for storage
    const reader = new FileReader();
    reader.onload = async (e) => {
      if (!e.target || typeof e.target.result !== 'string') return;

      const base64Data = e.target.result.split(',')[1]; // Remove data URL prefix

      try {
        let newResume: Partial<Resume>;

        // If it's a PDF file, try to extract and parse the content
        if (file.type === 'application/pdf') {
          try {
            // Show parsing toast
            toast({
              title: 'Processing PDF',
              description: 'Extracting and parsing resume content...',
              status: 'info',
              duration: 2000,
              isClosable: true,
            });

            // Extract text from PDF
            const pdfText = await extractPDFText(file);

            // Parse the extracted text using AI
            const parsedData = await parsePDFContent({
              pdfText,
              fileName: file.name
            });

            // Create resume with parsed data
            newResume = {
              ...parsedData,
              fileData: {
                name: file.name,
                type: file.type,
                data: base64Data
              }
            };

            toast({
              title: 'PDF parsed successfully',
              description: 'Resume content has been extracted and structured.',
              status: 'success',
              duration: 3000,
              isClosable: true,
            });

          } catch (parseError) {
            console.error('Error parsing PDF:', parseError);

            // Fallback to basic file upload if parsing fails
            newResume = {
              title: file.name.replace(/\.[^/.]+$/, ""), // Remove file extension
              personalInfo: {
                fullName: '',
                email: '',
                phone: '',
                location: '',
              },
              summary: 'Uploaded Resume - Parsing failed, please edit manually',
              experience: [],
              education: [],
              skills: [],
              fileData: {
                name: file.name,
                type: file.type,
                data: base64Data
              }
            };

            toast({
              title: 'PDF parsing failed',
              description: 'Resume uploaded but content parsing failed. You can edit it manually.',
              status: 'warning',
              duration: 5000,
              isClosable: true,
            });
          }
        } else {
          // For non-PDF files, create basic resume entry
          newResume = {
            title: file.name.replace(/\.[^/.]+$/, ""), // Remove file extension
            personalInfo: {
              fullName: '',
              email: '',
              phone: '',
              location: '',
            },
            summary: 'Uploaded Resume',
            experience: [],
            education: [],
            skills: [],
            fileData: {
              name: file.name,
              type: file.type,
              data: base64Data
            }
          };
        }

        const createdResume = await createResume(newResume);
        toast({
          title: 'Resume uploaded',
          description: 'Your resume has been uploaded successfully.',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });

        // Select the newly created resume
        if (createdResume) {
          handleSelectResume(createdResume as Resume); // Use handleSelectResume to set up preview
          refetch(); // Refetch the list of resumes
        }
      } catch (error) {
        console.error('Error uploading resume:', error);
        toast({
          title: 'Error',
          description: 'Failed to upload resume. Please try again.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      }
    };

    reader.readAsDataURL(file);
  }

  // Clean up PDF URL when component unmounts
  useEffect(() => {
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [pdfUrl]);



  const handleDeleteResume = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this resume?')) {
      try {
        await deleteResume({ id });

        // Clean up template manager mapping for this resume
        templateManager.removeResumeTemplate(id);

        toast({
          title: 'Resume deleted',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });

        // If the deleted resume was selected, clear selection and preview
        if (selectedResume && selectedResume.id === id) {
          setSelectedResume(null);
          setPdfUrl(null);
          setIsPdfReady(false);
          setFileType(null);
          setTotalPages(1);
          setCurrentPage(1);
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to delete resume. Please try again.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      }
    }
  };

  const handleCreateResume = () => {
    if (externalOnCreateResume) {
      externalOnCreateResume();
      return;
    }

    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to create resumes.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    setSelectedResume(null); // Clear selected resume when creating a new one
    setInternalIsCreatingResume(true);
    setCreationStep('template'); // Start with template selection
    setSelectedTemplate(null); // Clear any previously selected template
  };

  const handleCancelCreate = () => {
    if (externalOnCancelCreate) {
      externalOnCancelCreate();
      return;
    }

    setInternalIsCreatingResume(false);
    setCreationStep('template');
    setSelectedTemplate(null);
    // If there are resumes, select the first one
    if (resumes && resumes.length > 0) {
      handleSelectResume(resumes[0]);
    }
  };

  const onFileUpload = externalOnFileUpload || internalOnFileUpload;

  const handleTemplateSelect = async (template: ResumeTemplate) => {
    setSelectedTemplate(template);

    // If we're editing an existing resume, apply the template immediately
    if (selectedResume && !selectedResume.fileData) {
      try {
        // Apply the template change without showing toast in handleTemplateChange
        await handleTemplateChange(template, false);

        // Show the success toast here instead
        toast({
          title: 'Template updated',
          description: 'Resume template has been changed successfully.',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } catch (error) {
        // Error toast is already handled in handleTemplateChange
        console.error('Error changing template:', error);
      }
    }
  };

  const handleTemplateContinue = () => {
    if (selectedTemplate) {
      setCreationStep('form');
    }
  };

  const handleTemplateBack = () => {
    setCreationStep('template');
  };

  const handleSubmitResume = async (data: Partial<Resume>) => {
    try {
      // Include template information if a template was selected
      const resumeData = {
        ...data,
        templateId: selectedTemplate?.id
      };

      const createdResume = await createResume(resumeData);

      // Store template mapping for the new resume
      if (createdResume && selectedTemplate) {
        templateManager.setTemplateForResume(createdResume.id, selectedTemplate.id);
      }

      toast({
        title: 'Resume created successfully',
        description: 'Your resume has been created and is ready for preview.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Select the newly created resume and go directly to PDF preview
      if (createdResume) {
         const resume = createdResume as Resume;
         setSelectedResume(resume);
         setInternalIsCreatingResume(false);
         setCreationStep('template'); // Reset creation step for future use

         // Generate PDF preview for the newly created resume
         await generatePreviewForCreatedResume(resume);

         // Keep the selected template for preview generation
         // setSelectedTemplate(null); // Don't clear template yet
         refetch();

         // The resume is now selected and preview is generated
         // The UI will automatically show the PDF preview section

         // Call the callback to notify parent that resume was created
         onResumeCreated?.();
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create resume. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  if (!user) {
    return (
      <EmptyResumes
        title="Authentication Required"
        description="Please log in to access your resumes and start building your career profile."
        showActions={false}
      />
    );
  }

  if (isLoading) {
    return <SimpleLoading message="Loading your resumes..." />;
  }

  if (error) {
    return (
      <GeneralError
        error={error}
        onRetry={() => refetch()}
        showErrorDetails={false}
      />
    );
  }

  return (
    <VStack spacing={spacing} align="stretch" width="100%" height="100%">
      {/* Hidden file input for external use */}
      <Input
        type="file"
        accept="application/pdf,.pdf,image/*,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        onChange={onFileUpload}
        display="none"
        ref={fileInputRef}
      />

      {/* Top Section: Resume Selection & Actions */}
      {!isCreatingResume && resumes && resumes.length > 0 && (
        <Flex
          direction={{ base: 'column', md: 'row' }}
          gap={4}
          align={{ base: 'stretch', md: 'center' }}
          justify="space-between"
          p={4}
          bg={topSectionBg}
          borderRadius="lg"
          border="1px"
          borderColor={topSectionBorderColor}
        >
          {/* Editable Resume Selection - Left Aligned */}
          <VStack spacing={2} flex="1" align="stretch">
            {/* Section Header with Counter */}
            <HStack justify="space-between" align="center" w="100%">
              <HStack spacing={3} align="center">
                <Text fontSize="sm" fontWeight="semibold" color={selectLabelColor}>
                  My Resumes
                </Text>
                <Badge
                  colorScheme="purple"
                  variant="subtle"
                  fontSize="xs"
                  px={2}
                  py={1}
                  borderRadius="full"
                >
                  {resumes.length} resume{resumes.length !== 1 ? 's' : ''}
                </Badge>
              </HStack>
            </HStack>

            {/* Resume Selector with Editable Title */}
            {selectedResume ? (
              <VStack spacing={3} align="flex-start" w="100%">
                {/* Status Tag (Superscript) - Always present for consistent layout */}
                <HStack spacing={0} align="flex-start" w="100%" minH="20px">
                  {selectedResume.fileData ? (
                    <Badge
                      colorScheme="blue"
                      variant="solid"
                      size="sm"
                      fontSize="2xs"
                      px={2}
                      py={0.5}
                      borderRadius="md"
                      fontWeight="medium"
                      textTransform="uppercase"
                      letterSpacing="wide"
                    >
                      Uploaded File
                    </Badge>
                  ) : selectedResume.templateId ? (
                    <Badge
                      colorScheme="purple"
                      variant="solid"
                      size="sm"
                      fontSize="2xs"
                      px={2}
                      py={0.5}
                      borderRadius="md"
                      fontWeight="medium"
                      textTransform="uppercase"
                      letterSpacing="wide"
                    >
                      Template Applied
                    </Badge>
                  ) : (
                    <Badge
                      colorScheme="gray"
                      variant="subtle"
                      size="sm"
                      fontSize="2xs"
                      px={2}
                      py={0.5}
                      borderRadius="md"
                      fontWeight="medium"
                      textTransform="uppercase"
                      letterSpacing="wide"
                    >
                      Custom Resume
                    </Badge>
                  )}
                </HStack>

                {/* Editable Title with Edit Button */}
                <HStack spacing={1} align="center" w="100%">
                  {isEditingTitle ? (
                    <HStack spacing={2} flex="1">
                      <Input
                        value={editableTitle}
                        onChange={(e) => setEditableTitle(e.target.value)}
                        fontSize={{ base: 'md', md: 'lg' }}
                        fontWeight="semibold"
                        variant="flushed"
                        size="md"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') handleTitleSave();
                          if (e.key === 'Escape') handleTitleCancel();
                        }}
                        autoFocus
                      />
                      <IconButton
                        aria-label="Save title"
                        icon={<FaCheck />}
                        size="xs"
                        colorScheme="green"
                        variant="ghost"
                        onClick={handleTitleSave}
                      />
                      <IconButton
                        aria-label="Cancel edit"
                        icon={<FaTimes />}
                        size="xs"
                        colorScheme="red"
                        variant="ghost"
                        onClick={handleTitleCancel}
                      />
                    </HStack>
                  ) : (
                    <HStack spacing={1} align="center" w="100%">
                      <Text
                        fontSize={{ base: 'md', md: 'lg' }}
                        fontWeight="semibold"
                        color={useColorModeValue('gray.800', 'white')}
                        lineHeight="1.2"
                        cursor="pointer"
                        onClick={handleTitleEdit}
                        _hover={{ color: 'purple.500' }}
                        transition="color 0.2s"
                      >
                        {editableTitle || selectedResume.title}
                      </Text>
                      <IconButton
                        aria-label="Edit title"
                        icon={<FaEdit />}
                        size="xs"
                        variant="ghost"
                        onClick={handleTitleEdit}
                        opacity={0.7}
                        _hover={{
                          opacity: 1,
                          bg: useColorModeValue('gray.100', 'gray.700'),
                          transform: 'scale(1.1)'
                        }}
                        transition="all 0.2s"
                        borderRadius="md"
                        ml={1}
                      />
                    </HStack>
                  )}
                </HStack>

                {/* Description Subtitle */}
                <Text
                  fontSize={{ base: 'xs', md: 'sm' }}
                  color={useColorModeValue('gray.600', 'gray.300')}
                  lineHeight="1.4"
                >
                  Manage and preview your existing resumes
                </Text>

                {/* Resume Selector */}
                <Select
                  value={selectedResume.id}
                  onChange={(e) => {
                    const resume = resumes.find(r => r.id === e.target.value);
                    if (resume) handleSelectResume(resume);
                  }}
                  size="sm"
                  bg={selectBg}
                  borderColor={selectBorderColor}
                  maxW="250px"
                  fontSize="sm"
                >
                  {resumes.map((resume) => (
                    <option key={resume.id} value={resume.id}>
                      {resume.title}
                    </option>
                  ))}
                </Select>
              </VStack>
            ) : (
              <VStack spacing={3} align="flex-start" w="100%">
                {/* Empty State Title */}
                <Text
                  fontSize={{ base: 'md', md: 'lg' }}
                  fontWeight="semibold"
                  color={useColorModeValue('gray.800', 'white')}
                  lineHeight="1.2"
                >
                  Choose Resume
                </Text>

                {/* Description Subtitle */}
                <Text
                  fontSize={{ base: 'xs', md: 'sm' }}
                  color={useColorModeValue('gray.600', 'gray.300')}
                  lineHeight="1.4"
                >
                  Manage and preview your existing resumes
                </Text>

                {/* Resume Selector */}
                <Select
                  placeholder="Choose a resume to preview"
                  onChange={(e) => {
                    const resume = resumes.find(r => r.id === e.target.value);
                    if (resume) handleSelectResume(resume);
                  }}
                  size="sm"
                  bg={selectBg}
                  borderColor={selectBorderColor}
                  maxW="280px"
                  fontSize="sm"
                  _hover={{
                    borderColor: useColorModeValue('purple.300', 'purple.500')
                  }}
                  _focus={{
                    borderColor: 'purple.500',
                    boxShadow: '0 0 0 1px var(--chakra-colors-purple-500)'
                  }}
                  transition="all 0.2s"
                >
                  {resumes.map((resume) => (
                    <option key={resume.id} value={resume.id}>
                      {resume.title}
                    </option>
                  ))}
                </Select>
              </VStack>
            )}
          </VStack>

          {/* Action Buttons - Right Aligned */}
          <HStack spacing={2} flexWrap="wrap">
            {/* Resume-specific actions */}
            {selectedResume && (
              <>
                <Tooltip label="View resume" hasArrow>
                  <IconButton
                    aria-label="View resume"
                    icon={<FaEye />}
                    size="sm"
                    variant="ghost"
                    onClick={onViewModalOpen}
                    isDisabled={!pdfUrl}
                  />
                </Tooltip>
                <Tooltip label="Download resume" hasArrow>
                  <IconButton
                    aria-label="Download resume"
                    icon={<DownloadIcon />}
                    size="sm"
                    variant="ghost"
                    onClick={() => {
                      if (selectedResume.fileData) {
                        const link = document.createElement('a');
                        link.href = pdfUrl || '';
                        link.download = selectedResume.fileData?.name || 'resume';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                      } else {
                        handleSaveAsPDF();
                      }
                    }}
                  />
                </Tooltip>
                {!selectedResume.fileData && (
                  <Tooltip label="Print resume" hasArrow>
                    <IconButton
                      aria-label="Print resume"
                      icon={<FaPrint />}
                      size="sm"
                      variant="ghost"
                      onClick={handlePrintResume}
                    />
                  </Tooltip>
                )}
                <Tooltip label="Delete resume" hasArrow>
                  <IconButton
                    aria-label="Delete resume"
                    icon={<FaTrash />}
                    size="sm"
                    variant="ghost"
                    colorScheme="red"
                    onClick={() => handleDeleteResume(selectedResume.id)}
                  />
                </Tooltip>
              </>
            )}
          </HStack>
        </Flex>
      )}

      {/* Main Content Area */}
      <Box flex="1" height="100%">
        {!isCreatingResume && (!resumes || resumes.length === 0) ? (
          <ContentContainer
            height="100%"
            minHeight="400px"
            p={6}
            borderRadius="lg"
            border="1px"
            borderColor={containerBorderColor}
          >
            <EmptyResumes
              size="md"
              primaryAction={{
                label: 'Upload Resume',
                onClick: handleFileButtonClick,
                icon: <FaUpload />,
                colorScheme: 'blue',
              }}
              secondaryAction={{
                label: 'Create New',
                onClick: handleCreateResume,
                icon: <FaPlus />,
                variant: 'outline',
              }}
            />
          </ContentContainer>
        ) : isCreatingResume ? (
            <Grid
              templateColumns={{ base: "1fr", lg: "400px 1fr" }}
              gap={{ base: 0, md: 2, lg: 4 }}
              height="100%"
              minHeight="400px"
            >
              {/* Left Panel: Creation Steps */}
              <GridItem>
                <ContentContainer
                  height="100%"
                  minHeight={{ base: "300px", lg: "calc(100vh - 300px)" }}
                  maxHeight={{ base: "none", lg: "calc(100vh - 300px)" }}
                  overflowY="auto"
                  delay={0.3}
                  p={{ base: 2, md: 4 }}
                  borderRadius="lg"
                  border="1px"
                  borderColor={containerBorderColor}
                >
                  <VStack spacing={3} align="stretch" height="100%">
                    {creationStep === 'template' ? (
                      <>
                        {/* Header */}
                        <VStack spacing={3} align="flex-start">
                          <Heading size="md" color="purple.500">
                            Create New Resume
                          </Heading>
                          <Text color="gray.500" fontSize="sm" lineHeight="1.5">
                            Choose a professional template to get started. You can customize everything later.
                          </Text>
                        </VStack>

                        {/* Quick Start Options */}
                        <VStack spacing={3} align="stretch">
                          <Text fontSize="sm" fontWeight="medium" color="gray.700">
                            Quick Start Options:
                          </Text>

                          <Button
                            leftIcon={<FaPlus />}
                            colorScheme="purple"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // Skip template selection and go directly to form
                              setCreationStep('form');
                              setSelectedTemplate(null);
                            }}
                            width="100%"
                          >
                            Start from Scratch
                          </Button>

                          <Text fontSize="xs" color="gray.500" textAlign="center">
                            or
                          </Text>

                          <Text fontSize="sm" fontWeight="medium" color="gray.700">
                            Choose from Templates:
                          </Text>
                        </VStack>

                        {/* Selected Template Display */}
                        {selectedTemplate && (
                          <Box
                            bg={templateSelectedBg}
                            border="2px solid"
                            borderColor="purple.500"
                            borderRadius="lg"
                            p={4}
                          >
                            <VStack align="start" spacing={2}>
                              <HStack spacing={2}>
                                <Text fontSize="lg">✓</Text>
                                <Text fontWeight="bold" color="purple.700" fontSize="md">
                                  {selectedTemplate.name}
                                </Text>
                              </HStack>
                              <Text fontSize="sm" color="gray.600" lineHeight="1.4">
                                {selectedTemplate.description}
                              </Text>
                              <Button
                                rightIcon={<FaPlus />}
                                colorScheme="purple"
                                onClick={handleTemplateContinue}
                                size="sm"
                                width="100%"
                                mt={2}
                              >
                                Continue with {selectedTemplate.name}
                              </Button>
                            </VStack>
                          </Box>
                        )}

                        {/* Spacer to push buttons to bottom */}
                        <Box flex="1" />

                        {/* Cancel Button */}
                        <Box pt={3} borderTop="1px solid" borderColor="gray.200">
                          <Button
                            leftIcon={<CloseIcon />}
                            variant="ghost"
                            onClick={handleCancelCreate}
                            width="100%"
                            size="sm"
                            colorScheme="gray"
                          >
                            Cancel
                          </Button>
                        </Box>
                      </>
                    ) : (
                      <>
                        {/* Form Step Header */}
                        <VStack spacing={3} align="flex-start">
                          <Heading size="md" color="purple.500">
                            Fill Your Details
                          </Heading>
                          <Text color="gray.500" fontSize="sm" lineHeight="1.5">
                            Complete the form to create your professional resume. All fields are optional - add what's relevant for you.
                          </Text>
                        </VStack>

                        {/* Selected Template Info */}
                        {selectedTemplate ? (
                          <Box
                            bg={templateSelectedBg}
                            border="1px solid"
                            borderColor="purple.200"
                            borderRadius="md"
                            p={3}
                          >
                            <HStack spacing={2}>
                              <Text fontSize="sm" fontWeight="medium" color="purple.700">
                                Template: {selectedTemplate.name}
                              </Text>
                            </HStack>
                          </Box>
                        ) : (
                          <Box
                            bg="blue.50"
                            border="1px solid"
                            borderColor="blue.200"
                            borderRadius="md"
                            p={3}
                          >
                            <Text fontSize="sm" fontWeight="medium" color="blue.700">
                              Creating from scratch - no template
                            </Text>
                          </Box>
                        )}

                        {/* Progress Indicator */}
                        <VStack spacing={2} align="stretch">
                          <Text fontSize="xs" fontWeight="medium" color="gray.600">
                            STEP 2 OF 2
                          </Text>
                          <Box bg="gray.200" borderRadius="full" height="2px">
                            <Box bg="purple.500" borderRadius="full" height="100%" width="100%" />
                          </Box>
                        </VStack>

                        {/* Spacer */}
                        <Box flex="1" />

                        {/* Back Button */}
                        <Box pt={3} borderTop="1px solid" borderColor="gray.200">
                          <Button
                            leftIcon={<FaChevronLeft />}
                            variant="ghost"
                            onClick={handleTemplateBack}
                            width="100%"
                            size="sm"
                            colorScheme="gray"
                          >
                            Back to Templates
                          </Button>
                        </Box>
                      </>
                    )}
                  </VStack>
                </ContentContainer>
              </GridItem>

              {/* Right Panel: Template Gallery or Resume Form */}
              <GridItem>
                <ContentContainer
                  height="100%"
                  minHeight={{ base: "400px", lg: "calc(100vh - 300px)" }}
                  maxHeight={{ base: "none", lg: "calc(100vh - 300px)" }}
                  overflowY="auto"
                  delay={0.4}
                  p={{ base: 2, md: 4 }}
                  borderRadius="lg"
                  border="1px"
                  borderColor={containerBorderColor}
                >
                  {creationStep === 'template' ? (
                    <VStack spacing={4} align="stretch" height="100%">
                      <Heading size="sm" color="purple.500">
                        Choose Resume Template
                      </Heading>
                      <Box flex="1" overflowY="auto">
                        <ResumeTemplateGallery
                          selectedTemplate={selectedTemplate || undefined}
                          onTemplateSelect={handleTemplateSelect}
                        />
                      </Box>
                    </VStack>
                  ) : (
                    <VStack spacing={3} align="stretch" height="100%">
                      <Heading size="sm" color="purple.500">Create New Resume</Heading>
                      <Box flex="1" overflowY="auto">
                        <ResumeForm
                          onSubmit={handleSubmitResume}
                          onCancel={handleTemplateBack}
                          initialData={selectedTemplate ? {
                            title: `${selectedTemplate.name} Resume`,
                            ...selectedTemplate.sampleData
                          } : undefined}
                        />
                      </Box>
                    </VStack>
                  )}
                </ContentContainer>
              </GridItem>
            </Grid>
          ) : (
            // Single Preview Panel for Resume Viewing
            <ContentContainer
              height="100%"
              minHeight={{ base: "400px", lg: "calc(100vh - 300px)" }}
              maxHeight={{ base: "none", lg: "calc(100vh - 300px)" }}
              overflowY="auto"
              ref={pdfContainerRef}
              delay={0.4}
              p={{ base: 1, md: 2 }}
              borderRadius="lg"
              border="1px"
              borderColor={containerBorderColor}
            >
              {!selectedResume ? (
                <VStack spacing={6} align="center" justify="center" height="100%" p={8}>
                  <VStack spacing={3} align="center">
                    <Text fontSize="xl" fontWeight="medium" color="gray.600" textAlign="center">
                      No Resume Selected
                    </Text>
                    <Text fontSize="md" color="gray.500" textAlign="center" maxW="400px">
                      Choose a resume from the dropdown above to view and manage it, or use the action buttons to create or upload a new one.
                    </Text>
                  </VStack>
                </VStack>
              ) : (
                <VStack spacing={2} align="stretch" height="100%">
                  {/* Resume Preview Container */}
                  <Flex justify="center" align="flex-start" flex="1" pt={2} px={4} pb={4}>
                    <Box
                      width={{ base: "100%", md: "500px", lg: "600px" }}
                      height={{ base: "500px", md: "600px", lg: "700px" }}
                      bg={useColorModeValue('white', 'gray.800')}
                      border="1px solid"
                      borderColor={useColorModeValue('gray.200', 'gray.600')}
                      borderRadius="lg"
                      overflow="hidden"
                      position="relative"
                      boxShadow="lg"
                    >
                    {isPdfReady && pdfUrl ? (
                      fileType?.includes('image') ? (
                        <img
                          src={pdfUrl}
                          alt="Resume Preview"
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'contain',
                            backgroundColor: 'white'
                          }}
                        />
                      ) : fileType?.includes('pdf') ? (
                        <VStack spacing={0} height="100%">
                          {/* PDF Controls */}
                          <HStack
                            spacing={1}
                            p={1}
                            bg={useColorModeValue('gray.50', 'gray.700')}
                            borderBottom="1px solid"
                            borderColor={useColorModeValue('gray.200', 'gray.600')}
                            width="100%"
                            justify="space-between"
                          >
                            <HStack spacing={1}>
                              <IconButton
                                aria-label="Previous page"
                                icon={<FaChevronLeft />}
                                size="xs"
                                variant="ghost"
                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                isDisabled={currentPage <= 1}
                              />
                              <Text fontSize="xs" color="gray.600" minW="40px" textAlign="center">
                                {currentPage}/{totalPages}
                              </Text>
                              <IconButton
                                aria-label="Next page"
                                icon={<FaChevronRight />}
                                size="xs"
                                variant="ghost"
                                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                isDisabled={currentPage >= totalPages}
                              />
                            </HStack>
                            <HStack spacing={1}>
                              <IconButton
                                aria-label="Zoom out"
                                icon={<Text fontSize="xs">−</Text>}
                                size="xs"
                                variant="ghost"
                                onClick={() => setPdfScale(Math.max(0.5, pdfScale - 0.1))}
                                isDisabled={pdfScale <= 0.5}
                              />
                              <Text fontSize="xs" color="gray.600" minW="30px" textAlign="center">
                                {Math.round(pdfScale * 100)}%
                              </Text>
                              <IconButton
                                aria-label="Zoom in"
                                icon={<Text fontSize="xs">+</Text>}
                                size="xs"
                                variant="ghost"
                                onClick={() => setPdfScale(Math.min(2, pdfScale + 0.1))}
                                isDisabled={pdfScale >= 2}
                              />
                            </HStack>
                          </HStack>

                          {/* PDF Viewer */}
                          <Box flex="1" width="100%" overflow="hidden">
                            <iframe
                              key={`pdf-${currentPage}-${pdfScale}`}
                              src={`${pdfUrl}#page=${currentPage}&zoom=page-fit&view=FitH`}
                              width="100%"
                              height="100%"
                              style={{ border: 'none' }}
                              title="Resume Preview"
                            />
                          </Box>
                        </VStack>
                      ) : (
                        <iframe
                          src={pdfUrl}
                          width="100%"
                          height="100%"
                          style={{
                            border: 'none',
                            transform: 'scale(0.85)',
                            transformOrigin: 'top left',
                            width: '117.6%',
                            height: '117.6%'
                          }}
                          title="Resume Preview"
                        />
                      )
                    ) : (
                      <VStack spacing={4} align="center" justify="center" height="100%">
                        <Spinner size="lg" color="purple.500" />
                        <Text color="gray.500" fontSize="sm">Loading preview...</Text>
                      </VStack>
                    )}
                    </Box>
                  </Flex>
                </VStack>
              )}
            </ContentContainer>
          )}
      </Box>

      {/* Resume View Modal */}
      <Modal isOpen={isViewModalOpen} onClose={onViewModalClose} size="6xl" scrollBehavior="inside">
        <ModalOverlay backdropFilter="auto" backdropInvert="15%" backdropBlur="2px" />
        <ModalContent maxH="90vh">
          <ModalHeader>
            <VStack align="flex-start" spacing={3} w="100%">
              {/* Title */}
              <HStack spacing={3} align="center" flexWrap="wrap">
                <Text fontSize={{ base: 'lg', md: 'xl' }} fontWeight="semibold" color={useColorModeValue('gray.800', 'white')}>
                  {editableTitle || selectedResume?.title || 'Resume Preview'}
                </Text>
                {selectedResume?.fileData && (
                  <Badge
                    colorScheme="blue"
                    variant="solid"
                    px={2}
                    py={1}
                    borderRadius="md"
                    fontSize="xs"
                    fontWeight="medium"
                  >
                    Uploaded
                  </Badge>
                )}
              </HStack>



              {/* Template Badge */}
              {selectedResume?.templateId && (
                <Badge
                  colorScheme="purple"
                  variant="subtle"
                  px={3}
                  py={1}
                  borderRadius="full"
                  fontSize="xs"
                  fontWeight="medium"
                >
                  Template Applied
                </Badge>
              )}
            </VStack>
          </ModalHeader>
          <ModalCloseButton />

          <ModalBody>
            <VStack spacing={4} align="stretch">
              {/* Full Size Preview */}
              <Box
                width="100%"
                height="600px"
                bg={useColorModeValue('white', 'gray.800')}
                border="1px solid"
                borderColor={useColorModeValue('gray.200', 'gray.600')}
                borderRadius="md"
                overflow="hidden"
                position="relative"
              >
                {isPdfReady && pdfUrl ? (
                  fileType?.includes('image') ? (
                    <img
                      src={pdfUrl}
                      alt="Resume Preview"
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'contain',
                        backgroundColor: 'white'
                      }}
                    />
                  ) : (
                    <iframe
                      src={fileType?.includes('pdf') ? `${pdfUrl}#page=1&zoom=page-fit&view=FitH` : pdfUrl}
                      width="100%"
                      height="100%"
                      style={{ border: 'none' }}
                      title="Resume Preview"
                    />
                  )
                ) : (
                  <VStack spacing={4} align="center" justify="center" height="100%">
                    <Spinner size="lg" color="purple.500" />
                    <Text color="gray.500">Loading preview...</Text>
                  </VStack>
                )}
              </Box>

              {/* Resume Summary */}
              {selectedResume?.summary && selectedResume.summary.trim() && (
                <Box>
                  <Text fontSize="md" fontWeight="semibold" mb={2}>
                    Summary
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    {selectedResume.summary}
                  </Text>
                </Box>
              )}
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </VStack>
  );
};

export default ResumeManager;
