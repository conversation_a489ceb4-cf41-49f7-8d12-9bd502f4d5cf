import React from 'react';
import {
  Box,
  Container,
  Flex,
  HStack,
  Link,
  SimpleGrid,
  Stack,
  Text,
  VStack,
  Icon,
  Divider,
  useColorModeValue,
  Button,
  Badge,
  Heading,
} from '@chakra-ui/react';
import { Link as RouterLink } from 'react-router-dom';
import { FaGithub, FaTwitter, FaLinkedin, FaEnvelope, FaHeart, FaRocket, FaStar } from 'react-icons/fa';
import { motion } from 'framer-motion';
import Logo from './Logo';
import ThemeSwitch from './ThemeSwitch';

const MotionBox = motion(Box);

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();
  
  // Theme-aware colors
  const bgGradient = useColorModeValue(
    'linear(to-t, gray.100, white)', 
    'linear(to-t, gray.900, gray.800)'
  );
  const cardBg = useColorModeValue('gray.50', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.600', 'gray.400');
  const headingColor = useColorModeValue('gray.800', 'white');
  const linkColor = useColorModeValue('brand.500', 'brand.400');
  const hoverColor = useColorModeValue('brand.600', 'brand.300');
  const accentColor = useColorModeValue('brand.500', 'brand.300');
  const footerBg = useColorModeValue('white', 'gray.800');

  return (
    <MotionBox
      as="footer"
      id="footer"
      role="contentinfo"
      aria-label="Site footer"
      width="100%"
      bgGradient={bgGradient}
      color={useColorModeValue('gray.800', 'white')}
      mt={16}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      position="relative"
      overflow="hidden"
    >
      {/* Background Pattern */}
      <Box
        position="absolute"
        top="0"
        left="0"
        right="0"
        bottom="0"
        opacity={useColorModeValue(0.03, 0.05)}
        bgImage="radial-gradient(circle at 25px 25px, rgba(128, 128, 128, 0.2) 2px, transparent 0)"
        bgSize="50px 50px"
        pointerEvents="none"
      />

      <Container maxW="95%" py={{ base: 12, md: 16 }} position="relative">
        {/* Main Footer Content */}
        <SimpleGrid
          columns={{ base: 2, sm: 2, lg: 4 }}
          spacing={{ base: 4, md: 8, lg: 12 }}
          mb={{ base: 6, md: 8, lg: 12 }}
        >
          {/* Brand Section */}
          <VStack align={{ base: "center", lg: "flex-start" }} spacing={{ base: 3, lg: 6 }} gridColumn={{ base: "1 / -1", lg: "auto" }}>
            <Box>
              <Logo />
            </Box>
            <VStack spacing={{ base: 2, lg: 4 }} align={{ base: "center", lg: "flex-start" }}>
              <Text
                fontSize={{ base: "sm", md: "md" }}
                color={textColor}
                textAlign={{ base: "center", lg: "left" }}
                lineHeight="relaxed"
                maxW="280px"
              >
                Your AI-powered career companion. Transform your job search with intelligent tools.
              </Text>
              <HStack spacing={2} display={{ base: "none", lg: "flex" }}>
                <Badge colorScheme="green" px={2} py={1} borderRadius="full" fontSize="xs">
                  <Icon as={FaRocket} mr={1} />
                  10K+ Users
                </Badge>
                <Badge colorScheme="yellow" px={2} py={1} borderRadius="full" fontSize="xs">
                  <Icon as={FaStar} mr={1} />
                  4.9 Rating
                </Badge>
              </HStack>
            </VStack>
          </VStack>

          {/* Quick Links */}
          <VStack align={{ base: "center", lg: "flex-start" }} spacing={{ base: 2, lg: 4 }}>
            <Heading size="sm" color={headingColor} fontWeight="700">
              Product
            </Heading>
            <VStack spacing={{ base: 1, lg: 3 }} align={{ base: "center", lg: "flex-start" }}>
              <Link
                as={RouterLink}
                to="/"
                color={textColor}
                _hover={{ color: linkColor, textDecoration: 'none' }}
                fontSize={{ base: "xs", lg: "sm" }}
                fontWeight="500"
                transition="all 0.2s"
              >
                Cover Letters
              </Link>
              <Link
                as={RouterLink}
                to="/resume"
                color={textColor}
                _hover={{ color: linkColor, textDecoration: 'none' }}
                fontSize={{ base: "xs", lg: "sm" }}
                fontWeight="500"
                transition="all 0.2s"
              >
                Resume Builder
              </Link>
              <Link
                as={RouterLink}
                to="/interview"
                color={textColor}
                _hover={{ color: linkColor, textDecoration: 'none' }}
                fontSize={{ base: "xs", lg: "sm" }}
                fontWeight="500"
                transition="all 0.2s"
                display={{ base: "none", lg: "block" }}
              >
                Interview Prep
              </Link>
              <Link
                as={RouterLink}
                to="/jobs"
                color={textColor}
                _hover={{ color: linkColor, textDecoration: 'none' }}
                fontSize={{ base: "xs", lg: "sm" }}
                fontWeight="500"
                transition="all 0.2s"
                display={{ base: "none", lg: "block" }}
              >
                Job Tracker
              </Link>
            </VStack>
          </VStack>

          {/* Resources */}
          <VStack align={{ base: "center", lg: "flex-start" }} spacing={{ base: 2, lg: 4 }}>
            <Heading size="sm" color={headingColor} fontWeight="700">
              Resources
            </Heading>
            <VStack spacing={{ base: 1, lg: 3 }} align={{ base: "center", lg: "flex-start" }}>
              <Link
                as={RouterLink}
                to="/help"
                color={textColor}
                _hover={{ color: linkColor, textDecoration: 'none' }}
                fontSize={{ base: "xs", lg: "sm" }}
                fontWeight="500"
                transition="all 0.2s"
                display={{ base: "none", lg: "block" }}
              >
                Help Center
              </Link>
              <Link
                as={RouterLink}
                to="/learning"
                color={textColor}
                _hover={{ color: linkColor, textDecoration: 'none' }}
                fontSize={{ base: "xs", lg: "sm" }}
                fontWeight="500"
                transition="all 0.2s"
              >
                Career Learning
              </Link>
              <Link
                href="mailto:<EMAIL>"
                color={textColor}
                _hover={{ color: linkColor, textDecoration: 'none' }}
                fontSize={{ base: "xs", lg: "sm" }}
                fontWeight="500"
                transition="all 0.2s"
              >
                Support
              </Link>
            </VStack>
          </VStack>

          {/* Company & Legal */}
          <VStack align={{ base: "center", lg: "flex-start" }} spacing={{ base: 2, lg: 4 }}>
            <Heading size="sm" color={headingColor} fontWeight="700">
              Company
            </Heading>
            <VStack spacing={{ base: 1, lg: 3 }} align={{ base: "center", lg: "flex-start" }}>
              <Link
                as={RouterLink}
                to="/privacy"
                color={textColor}
                _hover={{ color: linkColor, textDecoration: 'none' }}
                fontSize={{ base: "xs", lg: "sm" }}
                fontWeight="500"
                transition="all 0.2s"
              >
                Privacy Policy
              </Link>
              <Link
                as={RouterLink}
                to="/tos"
                color={textColor}
                _hover={{ color: linkColor, textDecoration: 'none' }}
                fontSize={{ base: "xs", lg: "sm" }}
                fontWeight="500"
                transition="all 0.2s"
              >
                Terms of Service
              </Link>
            </VStack>

            {/* Theme Toggle */}
            <Box pt={{ base: 1, lg: 2 }} display={{ base: "none", lg: "block" }}>
              <HStack spacing={2}>
                <ThemeSwitch />
                <Text fontSize="sm" color={textColor} fontWeight="500">
                  Theme
                </Text>
              </HStack>
            </Box>
          </VStack>
        </SimpleGrid>

        {/* Social Links - Hidden on mobile */}
        <Box textAlign="center" mb={{ base: 4, md: 12 }} display={{ base: "none", lg: "block" }}>
          <VStack spacing={4}>
            <Heading size="sm" color={headingColor}>
              Stay Connected
            </Heading>
            <HStack spacing={6} justify="center">
              <Box
                cursor="not-allowed"
                opacity={0.5}
                _hover={{ transform: 'none' }}
              >
                <Icon
                  as={FaGithub}
                  boxSize={6}
                  color={textColor}
                />
              </Box>
              <Box
                cursor="not-allowed"
                opacity={0.5}
                _hover={{ transform: 'none' }}
              >
                <Icon
                  as={FaTwitter}
                  boxSize={6}
                  color={textColor}
                />
              </Box>
              <Box
                cursor="not-allowed"
                opacity={0.5}
                _hover={{ transform: 'none' }}
              >
                <Icon
                  as={FaLinkedin}
                  boxSize={6}
                  color={textColor}
                />
              </Box>
              <Box
                cursor="not-allowed"
                opacity={0.5}
                _hover={{ transform: 'none' }}
              >
                <Icon
                  as={FaEnvelope}
                  boxSize={6}
                  color={textColor}
                />
              </Box>
            </HStack>
          </VStack>
        </Box>

        {/* Divider */}
        <Divider 
          borderColor={borderColor} 
          opacity={0.6}
          mb={{ base: 6, md: 8 }}
        />

        {/* Bottom Section */}
        <Flex
          direction={{ base: 'column', md: 'row' }}
          justify="space-between"
          align="center"
          gap={{ base: 4, md: 0 }}
        >
          <Text fontSize="sm" color={textColor} textAlign="center" fontWeight="500">
            © {currentYear} CareerDart. All rights reserved. Powered by: Orbeny Technologies.
          </Text>
          <HStack spacing={2}>
            <Text fontSize="sm" color={textColor} fontWeight="500">
              Made with
            </Text>
            <Icon as={FaHeart} color="red.400" />
            <Text fontSize="sm" color={textColor} fontWeight="500">
              for your career
            </Text>
          </HStack>
        </Flex>
      </Container>
    </MotionBox>
  );
};

export default Footer;
