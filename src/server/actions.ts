import { type Job, type CoverLetter, type User, type LearningProgress, type Badge, type UserBadge, type InboxMessage } from "wasp/entities";
import { HttpError } from "wasp/server";
import {
  type GenerateCoverLetter,
  type CreateJob,
  type UpdateCoverLetter,
  type EditCoverLetter,
  type GenerateEdit,
  type UpdateJob,
  type UpdateUser,
  type DeleteJob,
  type StripePayment,
  type StripeGpt4Payment,
  type StripeCreditsPayment,
  type ImportJobFromLinkedIn,
  type ImportJobFromUrl,
  type MarkMessageAsRead,
  type MarkMessageAsStarred,
  type ArchiveMessage,
  type DeleteMessage,
  type SendInboxMessage,
  type SendBroadcastMessage,
} from "wasp/server/operations";
import fetch from 'node-fetch';
import Stripe from 'stripe';

// Type definition for OpenAI API response is defined below

const stripe = new Stripe(process.env.STRIPE_KEY!, {
  apiVersion: '2023-08-16',
});

const DOMAIN = process.env.WASP_WEB_CLIENT_URL || 'https://careerdart.netlify.app';

const gptConfig = {
  completeCoverLetter: `You are a cover letter generator.
You will be given a job description along with the job applicant's resume.
You will write a cover letter for the applicant that matches their past experiences from the resume with the job description. Write the cover letter in the same language as the job description provided!
Rather than simply outlining the applicant's past experiences, you will give more detail and explain how those experiences will help the applicant succeed in the new job.
You will write the cover letter in a modern, professional style without being too formal, as a modern employee might do naturally.`,
  ideasForCoverLetter:
    "You are a cover letter idea generator. You will be given a job description along with the job applicant's resume. You will generate a bullet point list of ideas for the applicant to use in their cover letter. ",
};

type CoverLetterPayload = Pick<CoverLetter, 'title' | 'jobId'> & {
  content: string;
  description: string;
  isCompleteCoverLetter: boolean;
  temperature: number;
  gptModel: string;
};

type OpenAIResponse = {
  id?: string;
  object?: string;
  created?: number;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  choices?: Array<{
    index?: number;
    message?: {
      role?: string;
      content: string;
    };
    finish_reason?: string;
  }>;
  error?: {
    message?: string;
  };
};

async function checkIfUserPaid({ context }: { context: any }) {
  // Check if user is admin - admins get premium access automatically
  const isAdmin = context.user?.email?.includes('admin') || context.user?.email?.endsWith('@admin.careerdart.com');

  if (isAdmin) {
    // Admin users always have premium access
    return;
  }

  if (!context.user.hasPaid && !context.user.credits) {
    throw new HttpError(402, 'User must pay to continue');
  }
  if (context.user.subscriptionStatus === 'past_due') {
    throw new HttpError(402, 'Your subscription is past due. Please update your payment method.');
  }
}

export const generateCoverLetter: GenerateCoverLetter<CoverLetterPayload, CoverLetter> = async (
  { jobId, title, content, description, isCompleteCoverLetter, temperature, gptModel },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }
  await checkIfUserPaid({ context })

  let command;
  if (isCompleteCoverLetter) {
    command = gptConfig.completeCoverLetter;
  } else {
    command = gptConfig.ideasForCoverLetter;
  }

  console.log(' gpt model: ', gptModel);

  const payload = {
    model: gptModel,
    messages: [
      {
        role: 'system',
        content: command,
      },
      {
        role: 'user',
        content: `My Resume: ${content}. Job title: ${title} Job Description: ${description}.`,
      },
    ],
    temperature,
  };

  let json: OpenAIResponse;

  try {
    if (!context.user.hasPaid && !context.user.credits) {
      throw new HttpError(402, 'User has not paid or is out of credits');
    } else if (context.user.credits && !context.user.hasPaid) {
      console.log('decrementing credits \n\n');
      await context.entities.User.update({
        where: { id: context.user.id },
        data: {
          credits: {
            decrement: 1,
          },
        },
      });
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.OPENAI_API_KEY!}`,
      },
      method: 'POST',
      body: JSON.stringify(payload),
    });

    json = (await response.json()) as OpenAIResponse;

    if (json?.error) throw new HttpError(500, json?.error?.message || 'Something went wrong');

    return context.entities.CoverLetter.create({
      data: {
        title,
        content: json?.choices?.[0]?.message?.content || '',
        tokenUsage: json?.usage?.completion_tokens || 0,
        user: { connect: { id: context.user.id } },
        job: { connect: { id: jobId } },
      },
    });
  } catch (error: any) {
    if (!context.user.hasPaid && error?.statusCode != 402) {
      await context.entities.User.update({
        where: { id: context.user.id },
        data: {
          credits: {
            increment: 1,
          },
        },
      });
    }
    console.error(error);
    throw new HttpError(error.statusCode || 500, error.message || 'Something went wrong');
  }
};

export const generateEdit: GenerateEdit<
  { content: string; improvement: string },
  string
> = async ({ content, improvement }, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }
  await checkIfUserPaid({ context });

  let command;
  command = `You are a cover letter editor. You will be given a piece of isolated text from within a cover letter and told how you can improve it. Only respond with the revision. Make sure the revision is in the same language as the given isolated text.`;

  const payload = {
    model: context.user.gptModel === 'gpt-4' || context.user.gptModel === 'gpt-4o' ? 'gpt-4o' : 'gpt-4o-mini',
    messages: [
      {
        role: 'system',
        content: command,
      },
      {
        role: 'user',
        content: `Isolated text from within cover letter: ${content}. It should be improved by making it more: ${improvement}`,
      },
    ],
    temperature: 0.5,
  };

  let json: OpenAIResponse;

  try {
    if (!context.user.hasPaid && !context.user.credits) {
      throw new HttpError(402, 'User has not paid or is out of credits');
    } else if (context.user.credits && !context.user.hasPaid) {
      console.log('decrementing credits \n\n');
      await context.entities.User.update({
        where: { id: context.user.id },
        data: {
          credits: {
            decrement: 1,
          },
        },
      });
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.OPENAI_API_KEY!}`,
      },
      method: 'POST',
      body: JSON.stringify(payload),
    });

    json = (await response.json()) as OpenAIResponse;
    if (json?.choices?.[0]?.message?.content?.length) {
      return json.choices[0].message.content;
    } else {
      throw new HttpError(500, 'GPT returned an empty response');
    }
  } catch (error: any) {
    if (!context.user.hasPaid && error?.statusCode != 402) {
      await context.entities.User.update({
        where: { id: context.user.id },
        data: {
          credits: {
            increment: 1,
          },
        },
      });
    }
    console.error(error);
    throw new HttpError(error.statusCode || 500, error.message || 'Something went wrong');
  }
};

export type JobPayload = Pick<Job, 'title' | 'company' | 'location' | 'description'>;

export const createJob: CreateJob<JobPayload, Job> = ({ title, company, location, description }, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.Job.create({
    data: {
      title,
      description,
      location,
      company,
      user: { connect: { id: context.user.id } },
    },
  });
};

export type UpdateJobPayload = Pick<Job, 'id' | 'title' | 'company' | 'location' | 'description' | 'isCompleted'>;

export const updateJob: UpdateJob<UpdateJobPayload, Job> = (
  { id, title, company, location, description, isCompleted },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.Job.update({
    where: {
      id,
    },
    data: {
      title,
      description,
      location,
      company,
      isCompleted,
    },
  });
};

type LinkedInJobPayload = {
  url: string;
};

export const importJobFromLinkedIn: ImportJobFromLinkedIn<LinkedInJobPayload, JobPayload> = async (
  { url },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  // Validate LinkedIn URL
  if (!url.includes('linkedin.com/jobs/view/')) {
    throw new HttpError(400, 'Invalid LinkedIn job URL. Please provide a URL from linkedin.com/jobs/view/');
  }

  try {
    // Fetch the LinkedIn job page
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
    });

    if (!response.ok) {
      throw new HttpError(response.status, `Failed to fetch job data: ${response.statusText}`);
    }

    const html = await response.text();

    // Extract job title - LinkedIn uses h1 with specific classes
    const titleMatch = html.match(/<h1[^>]*class="[^"]*top-card-layout__title[^"]*"[^>]*>(.*?)<\/h1>/i) ||
                      html.match(/<h1[^>]*class="[^"]*job-title[^"]*"[^>]*>(.*?)<\/h1>/i) ||
                      html.match(/<h1[^>]*>(.*?)<\/h1>/i);
    const title = titleMatch ? cleanHtml(titleMatch[1]) : 'Unknown Position';

    // Extract company name - LinkedIn shows company in specific elements
    const companyMatch = html.match(/<a[^>]*class="[^"]*topcard__org-name-link[^"]*"[^>]*>(.*?)<\/a>/i) ||
                        html.match(/<span[^>]*class="[^"]*topcard__flavor[^"]*"[^>]*>(.*?)<\/span>/i) ||
                        html.match(/<a[^>]*class="[^"]*sub-nav-cta__optional-url[^"]*"[^>]*>(.*?)<\/a>/i) ||
                        html.match(/<span[^>]*class="[^"]*company-name[^"]*"[^>]*>(.*?)<\/span>/i) ||
                        html.match(/<a[^>]*class="[^"]*company[^"]*"[^>]*>(.*?)<\/a>/i);
    const company = companyMatch ? cleanHtml(companyMatch[1]) : 'Unknown Company';

    // Extract location - LinkedIn uses "Work Location" or similar patterns
    const locationMatch = html.match(/Work Location[^<]*<[^>]*>(.*?)<\/[^>]*>/i) ||
                         html.match(/Work Location.*?<span[^>]*>(.*?)<\/span>/i) ||
                         html.match(/<span[^>]*class="[^"]*topcard__flavor--bullet[^"]*"[^>]*>(.*?)<\/span>/i) ||
                         html.match(/<span[^>]*class="[^"]*job-criteria__text[^"]*"[^>]*>(.*?)<\/span>/i) ||
                         html.match(/<span[^>]*class="[^"]*topcard__flavor[^"]*"[^>]*>.*?·\s*(.*?)<\/span>/i) ||
                         html.match(/<span[^>]*class="[^"]*location[^"]*"[^>]*>(.*?)<\/span>/i) ||
                         html.match(/<span[^>]*class="[^"]*job-location[^"]*"[^>]*>(.*?)<\/span>/i);
    const location = locationMatch ? cleanHtml(locationMatch[1]) : 'Unknown Location';

    // Extract job description - LinkedIn uses specific description containers
    const descriptionMatch = html.match(/<div[^>]*class="[^"]*show-more-less-html__markup[^"]*"[^>]*>([\s\S]*?)<\/div>/i) ||
                            html.match(/<div[^>]*class="[^"]*description__text[^"]*"[^>]*>([\s\S]*?)<\/div>/i) ||
                            html.match(/<div[^>]*class="[^"]*description[^"]*"[^>]*>([\s\S]*?)<\/div>/i) ||
                            html.match(/<div[^>]*class="[^"]*job-description[^"]*"[^>]*>([\s\S]*?)<\/div>/i);
    const description = descriptionMatch ? cleanHtml(descriptionMatch[1]) : 'No description available';

    // If we couldn't extract the basic information, use OpenAI to help parse it
    if (title === 'Unknown Position' || company === 'Unknown Company' || description === 'No description available') {
      // Use OpenAI to extract job information from HTML
      const payload = {
        model: "gpt-4o-mini",
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that extracts job information from LinkedIn HTML. Focus on finding the job title, company name, work location, and job description. LinkedIn often shows location as "Work Location" and company names in topcard elements.',
          },
          {
            role: 'user',
            content: `Extract the job title, company name, work location, and full job description from this LinkedIn job posting HTML.

Key patterns to look for:
- Job title: Usually in h1 tags with "top-card-layout__title" or similar classes
- Company name: Often in "topcard__org-name-link" or "topcard__flavor" elements
- Location: Look for "Work Location" text or "topcard__flavor--bullet" elements
- Description: Usually in "show-more-less-html__markup" or "description__text" containers

Return the information in JSON format with keys: title, company, location, description.

HTML: ${html.substring(0, 15000)}...`,
          },
        ],
        response_format: { type: "json_object" },
      };

      const aiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.OPENAI_API_KEY!}`,
        },
        method: 'POST',
        body: JSON.stringify(payload),
      });

      if (!aiResponse.ok) {
        throw new HttpError(aiResponse.status, `Failed to parse job data: ${aiResponse.statusText}`);
      }

      const aiJson = await aiResponse.json() as OpenAIResponse;
      if (!aiJson.choices || !aiJson.choices[0] || !aiJson.choices[0].message) {
        throw new HttpError(500, 'Invalid response format from OpenAI');
      }
      const parsedData = JSON.parse(aiJson.choices[0].message.content);

      return {
        title: parsedData.title || title,
        company: parsedData.company || company,
        location: parsedData.location || location,
        description: parsedData.description || description,
      };
    }

    return {
      title,
      company,
      location,
      description,
    };
  } catch (error: any) {
    console.error('Error importing job from LinkedIn:', error);
    throw new HttpError(error.statusCode || 500, error.message || 'Failed to import job from LinkedIn');
  }
};

// Helper function to clean HTML content
function cleanHtml(html: string): string {
  return html
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&nbsp;/g, ' ') // Replace &nbsp; with spaces
    .replace(/&amp;/g, '&') // Replace &amp; with &
    .replace(/&lt;/g, '<') // Replace &lt; with <
    .replace(/&gt;/g, '>') // Replace &gt; with >
    .replace(/&quot;/g, '"') // Replace &quot; with "
    .replace(/&#39;/g, "'") // Replace &#39; with '
    .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
    .trim(); // Remove leading/trailing whitespace
}

export type UpdateCoverLetterPayload = Pick<Job, 'id' | 'description'> &
  Pick<CoverLetter, 'content'> & {
    isCompleteCoverLetter: boolean;
    temperature: number;
    gptModel: string;
  };

export const updateCoverLetter: UpdateCoverLetter<UpdateCoverLetterPayload, string> = async (
  { id, description, content, isCompleteCoverLetter, temperature, gptModel },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }
  await checkIfUserPaid({ context });

  const job = await context.entities.Job.findFirst({
    where: {
      id,
      user: { id: context.user.id },
    },
  });

  if (!job) {
    throw new HttpError(404, 'Job not found');
  }

  const coverLetter = await generateCoverLetter(
    {
      jobId: id,
      title: job.title,
      content,
      description: job.description,
      isCompleteCoverLetter,
      temperature,
      gptModel,
    },
    context
  );

  await context.entities.Job.update({
    where: {
      id,
    },
    data: {
      description,
      coverLetter: { connect: { id: coverLetter.id } },
    },
  });

  return coverLetter.id;
};

export const editCoverLetter: EditCoverLetter<{ coverLetterId: string; content: string }, CoverLetter> = (
  { coverLetterId, content },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  return context.entities.CoverLetter.update({
    where: {
      id: coverLetterId,
    },
    data: {
      content,
    },
  });
};

export const deleteJob: DeleteJob<{ jobId: string }, { count: number }> = ({ jobId }, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }
  if (!jobId) {
    throw new HttpError(401);
  }

  return context.entities.Job.deleteMany({
    where: {
      id: jobId,
      userId: context.user.id,
    },
  });
};

type UpdateUserArgs = Partial<Pick<User, 'id' | 'notifyPaymentExpires' | 'gptModel' | 'username' | 'bio' | 'profileImageUrl' | 'darkMode' | 'wordCountDisplay' | 'autoSave' | 'jobReminders' | 'featureUpdates' | 'subscriptionReminders' | 'marketingEmails' | 'yearsOfExperience'>>;
type UserWithoutPassword = Omit<User, 'password'>;

export const updateUser: UpdateUser<UpdateUserArgs, UserWithoutPassword> = async (
  {
    notifyPaymentExpires,
    gptModel,
    username,
    bio,
    profileImageUrl,
    darkMode,
    wordCountDisplay,
    autoSave,
    jobReminders,
    featureUpdates,
    subscriptionReminders,
    marketingEmails,
    yearsOfExperience
  },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  // Check if username is being updated and if it's already taken
  if (username && username !== context.user.username) {
    const existingUser = await context.entities.User.findUnique({
      where: { username },
    });
    if (existingUser && existingUser.id !== context.user.id) {
      throw new HttpError(400, 'Username is already taken');
    }
  }

  return context.entities.User.update({
    where: {
      id: context.user.id,
    },
    data: {
      notifyPaymentExpires,
      gptModel,
      username,
      bio,
      profileImageUrl,
      darkMode,
      wordCountDisplay,
      autoSave,
      jobReminders,
      featureUpdates,
      subscriptionReminders,
      marketingEmails,
      yearsOfExperience,
      updatedAt: new Date(),
    },
    select: {
      id: true,
      email: true,
      username: true,
      hasPaid: true,
      datePaid: true,
      notifyPaymentExpires: true,
      checkoutSessionId: true,
      stripeId: true,
      credits: true,
      gptModel: true,
      subscriptionStatus: true,
      yearsOfExperience: true,
      profileImageUrl: true,
      bio: true,
      darkMode: true,
      wordCountDisplay: true,
      autoSave: true,
      jobReminders: true,
      featureUpdates: true,
      subscriptionReminders: true,
      marketingEmails: true,
      createdAt: true,
      updatedAt: true,
      lastLoginAt: true,
    },
  });
};

// Delete user account action
export const deleteUserAccount = async (_args: void, context: any) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  try {
    // Delete user and all related data (cascade deletes will handle most relations)
    await context.entities.User.delete({
      where: {
        id: context.user.id,
      },
    });

    return { success: true, message: 'Account deleted successfully' };
  } catch (error) {
    console.error('Error deleting user account:', error);
    throw new HttpError(500, 'Failed to delete account');
  }
};

// Check email verification status action
export const checkEmailVerificationStatus = async (args: { email: string }, context: any) => {
  const { email } = args;

  if (!email) {
    throw new HttpError(400, 'Email is required');
  }

  try {
    // First check if user exists
    const user = await context.entities.User.findUnique({
      where: { email }
    });

    if (!user) {
      return { exists: false, verified: false, message: 'No account found with this email address.' };
    }

    // For now, we'll return that the user exists and let the login process handle verification
    // This is a simplified approach since accessing auth tables directly has TypeScript issues
    return { exists: true, verified: null, message: 'Account found. Please try logging in.' };

  } catch (error: any) {
    console.error('Error checking email verification status:', error);
    return { exists: false, verified: false, message: 'Unable to verify account status.' };
  }
};

type UpdateUserResult = Pick<User, 'id' | 'email' | 'hasPaid'>;

function dontUpdateUser(user: UserWithoutPassword): Promise<UserWithoutPassword> {
  return new Promise((resolve) => {
    resolve(user);
  });
}

type StripePaymentResult = {
  sessionUrl: string | null;
  sessionId: string;
};

export const stripePayment: StripePayment<void, StripePaymentResult> = async (_args, context) => {
  if (!context.user || !context.user.email) {
    throw new HttpError(401, 'User or email not found');
  }
  let customer: Stripe.Customer;
  const stripeCustomers = await stripe.customers.list({
    email: context.user.email,
  });
  if (!stripeCustomers.data.length) {
    console.log('creating customer');
    customer = await stripe.customers.create({
      email: context.user.email,
    });
  } else {
    console.log('using existing customer');
    customer = stripeCustomers.data[0];
  }

  const session: Stripe.Checkout.Session = await stripe.checkout.sessions.create({
    line_items: [
      {
        price: process.env.PRODUCT_PRICE_ID!,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: `${DOMAIN}/checkout?success=true`,
    cancel_url: `${DOMAIN}/checkout?canceled=true`,
    automatic_tax: { enabled: true },
    customer_update: {
      address: 'auto',
    },
    customer: customer.id,
  });

  await context.entities.User.update({
    where: {
      id: context.user.id,
    },
    data: {
      checkoutSessionId: session?.id ?? null,
      stripeId: customer.id ?? null,
    },
  });

  return new Promise((resolve, reject) => {
    if (!session) {
      reject(new HttpError(402, 'Could not create a Stripe session'));
    } else {
      resolve({
        sessionUrl: session.url,
        sessionId: session.id,
      });
    }
  });
};

export const stripeGpt4Payment: StripeGpt4Payment<void, StripePaymentResult> = async (_args, context) => {
  if (!context.user || !context.user.email) {
    throw new HttpError(401, 'User or email not found');
  }
  let customer: Stripe.Customer;
  const stripeCustomers = await stripe.customers.list({
    email: context.user.email,
  });
  if (!stripeCustomers.data.length) {
    console.log('creating customer');
    customer = await stripe.customers.create({
      email: context.user.email,
    });
  } else {
    console.log('using existing customer');
    customer = stripeCustomers.data[0];
  }

  const session: Stripe.Checkout.Session = await stripe.checkout.sessions.create({
    line_items: [
      {
        price: process.env.GPT4_PRICE_ID!,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: `${DOMAIN}/checkout?success=true`,
    cancel_url: `${DOMAIN}/checkout?canceled=true`,
    automatic_tax: { enabled: true },
    customer_update: {
      address: 'auto',
    },
    customer: customer.id,
  });

  await context.entities.User.update({
    where: {
      id: context.user.id,
    },
    data: {
      checkoutSessionId: session?.id ?? null,
      stripeId: customer.id ?? null,
    },
  });

  return new Promise((resolve, reject) => {
    if (!session) {
      reject(new HttpError(402, 'Could not create a Stripe session'));
    } else {
      resolve({
        sessionUrl: session.url,
        sessionId: session.id,
      });
    }
  });
};

export const stripeCreditsPayment: StripeCreditsPayment<void, StripePaymentResult> = async (_args, context) => {
  if (!context.user || !context.user.email) {
    throw new HttpError(401, 'User or email not found');
  }
  let customer: Stripe.Customer;
  const stripeCustomers = await stripe.customers.list({
    email: context.user.email,
  });
  if (!stripeCustomers.data.length) {
    console.log('creating customer');
    customer = await stripe.customers.create({
      email: context.user.email,
    });
  } else {
    console.log('using existing customer');
    customer = stripeCustomers.data[0];
  }

  const session: Stripe.Checkout.Session = await stripe.checkout.sessions.create({
    line_items: [
      {
        price: process.env.PRODUCT_CREDITS_PRICE_ID!,
        quantity: 1,
      },
    ],
    mode: 'payment',
    success_url: `${DOMAIN}/checkout?credits=true`,
    cancel_url: `${DOMAIN}/checkout?canceled=true`,
    automatic_tax: { enabled: true },
    customer_update: {
      address: 'auto',
    },
    customer: customer.id,
  });

  await context.entities.User.update({
    where: {
      id: context.user.id,
    },
    data: {
      stripeId: customer.id ?? null,
    },
  });

  return new Promise((resolve, reject) => {
    if (!session) {
      reject(new HttpError(402, 'Could not create a Stripe session'));
    } else {
      resolve({
        sessionUrl: session.url,
        sessionId: session.id,
      });
    }
  });
};

// Learning Progress Actions
type UpdateLearningProgressArgs = {
  resourceId: string;
  resourceType: string;
  progress: number;
  completed?: boolean;
  timeSpent?: number;
};

export const updateLearningProgress = async (args: UpdateLearningProgressArgs, context: any) => {
  if (!context.user) {
    throw new HttpError(401, 'User not authenticated');
  }

  const { resourceId, resourceType, progress, completed = false, timeSpent = 0 } = args;

  const learningProgress = await context.entities.LearningProgress.upsert({
    where: {
      userId_resourceId: {
        userId: context.user.id,
        resourceId: resourceId,
      },
    },
    update: {
      progress: Math.min(100, Math.max(0, progress)),
      completed: completed || progress >= 100,
      timeSpent: timeSpent,
      lastAccessed: new Date(),
    },
    create: {
      userId: context.user.id,
      resourceId: resourceId,
      resourceType: resourceType,
      progress: Math.min(100, Math.max(0, progress)),
      completed: completed || progress >= 100,
      timeSpent: timeSpent,
    },
  });

  // Check for badge eligibility after updating progress
  await checkAndAwardBadges(context.user.id, context);

  return learningProgress;
};

// Initialize badges in the system
export const initializeBadges = async (_args: void, context: any) => {
  const badgeCriteria = [
    {
      name: "First Steps",
      description: "Complete your first learning video",
      icon: "🎯",
      category: "learning",
      criteria: { completedVideos: 1 },
      color: "green"
    },
    {
      name: "Learning Enthusiast",
      description: "Complete 5 learning videos",
      icon: "📚",
      category: "learning",
      criteria: { completedVideos: 5 },
      color: "blue"
    },
    {
      name: "Knowledge Seeker",
      description: "Complete 10 learning videos",
      icon: "🎓",
      category: "learning",
      criteria: { completedVideos: 10 },
      color: "purple"
    },
    {
      name: "Time Investor",
      description: "Spend 1 hour learning",
      icon: "⏰",
      category: "learning",
      criteria: { timeSpent: 3600 },
      color: "orange"
    },
    {
      name: "Dedicated Learner",
      description: "Spend 5 hours learning",
      icon: "📖",
      category: "learning",
      criteria: { timeSpent: 18000 },
      color: "red"
    },
    {
      name: "Career Starter",
      description: "Complete your first career development video",
      icon: "🚀",
      category: "achievement",
      criteria: { completedVideos: 1 },
      color: "teal"
    }
  ];

  const createdBadges: any[] = [];
  for (const badgeData of badgeCriteria) {
    const existingBadge = await context.entities.Badge.findUnique({
      where: { name: badgeData.name }
    });

    if (!existingBadge) {
      const badge = await context.entities.Badge.create({
        data: badgeData
      });
      createdBadges.push(badge);
    }
  }

  return { message: `Initialized ${createdBadges.length} new badges`, badges: createdBadges };
};

// Badge System Functions
const checkAndAwardBadges = async (userId: number, context: any) => {
  // Get user's learning progress
  const userProgress = await context.entities.LearningProgress.findMany({
    where: { userId: userId },
  });

  const completedCount = userProgress.filter((p: any) => p.completed).length;
  const totalTimeSpent = userProgress.reduce((sum: number, p: any) => sum + p.timeSpent, 0);

  // Get all available badges
  const allBadges = await context.entities.Badge.findMany();

  // Check each badge criteria
  for (const badge of allBadges) {
    const criteria = badge.criteria as any;
    const meetsRequirement =
      (criteria.completedVideos && completedCount >= criteria.completedVideos) ||
      (criteria.timeSpent && totalTimeSpent >= criteria.timeSpent);

    if (meetsRequirement) {
      // Award badge to user if they don't have it
      const existingUserBadge = await context.entities.UserBadge.findUnique({
        where: {
          userId_badgeId: {
            userId: userId,
            badgeId: badge.id,
          }
        }
      });

      if (!existingUserBadge) {
        await context.entities.UserBadge.create({
          data: {
            userId: userId,
            badgeId: badge.id,
          }
        });
      }
    }
  }
};

// Performance Monitoring Action
type PerformanceMetric = {
  name: string;
  value: number;
  timestamp: number;
  url: string;
  userAgent: string;
  userId?: string;
  rating?: 'good' | 'needs-improvement' | 'poor';
  metadata?: any;
};

type ReportPerformanceMetricsArgs = {
  metrics: PerformanceMetric[];
};

export const reportPerformanceMetrics = async (args: ReportPerformanceMetricsArgs, context: any) => {
  const { metrics } = args;

  if (!metrics || !Array.isArray(metrics) || metrics.length === 0) {
    return { success: true, message: 'No metrics to report' };
  }

  try {
    // Log metrics for now (in production, you'd send to monitoring service)
    console.log('Performance Metrics Received:', {
      count: metrics.length,
      userId: context.user?.id,
      timestamp: new Date().toISOString(),
    });

    // Process each metric
    for (const metric of metrics) {
      // Validate metric structure
      if (!metric.name || typeof metric.value !== 'number') {
        continue;
      }

      // Log important metrics
      if (metric.name.includes('ERROR') || metric.rating === 'poor') {
        console.warn('Performance Issue Detected:', {
          metric: metric.name,
          value: metric.value,
          rating: metric.rating,
          url: metric.url,
          userId: context.user?.id,
          timestamp: new Date(metric.timestamp).toISOString(),
        });
      }

      // In production, you would:
      // 1. Store metrics in a time-series database (e.g., InfluxDB, TimescaleDB)
      // 2. Send to monitoring services (e.g., DataDog, New Relic, Sentry)
      // 3. Trigger alerts for critical performance issues
      // 4. Aggregate metrics for dashboards

      // Example: Store in database (you'd need to create a PerformanceMetric entity)
      // await context.entities.PerformanceMetric.create({
      //   data: {
      //     name: metric.name,
      //     value: metric.value,
      //     timestamp: new Date(metric.timestamp),
      //     url: metric.url,
      //     userAgent: metric.userAgent,
      //     userId: context.user?.id,
      //     rating: metric.rating,
      //     metadata: metric.metadata,
      //   },
      // });
    }

    return {
      success: true,
      message: `Successfully processed ${metrics.length} performance metrics`
    };
  } catch (error) {
    console.error('Error processing performance metrics:', error);
    // Don't throw error to avoid disrupting user experience
    return {
      success: false,
      message: 'Failed to process performance metrics'
    };
  }
};

type GenericJobUrlPayload = {
  url: string;
};

export const importJobFromUrl: ImportJobFromUrl<GenericJobUrlPayload, JobPayload> = async (
  { url },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  // Basic URL validation
  try {
    new URL(url);
  } catch {
    throw new HttpError(400, 'Invalid URL. Please provide a valid job posting URL.');
  }

  try {
    // Fetch the job page
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
    });

    if (!response.ok) {
      throw new HttpError(response.status, `Failed to fetch job data: ${response.statusText}`);
    }

    const html = await response.text();

    // Try basic HTML parsing for common job sites
    let title = 'Unknown Position';
    let company = 'Unknown Company';
    let location = 'Unknown Location';
    let description = 'No description available';

    // Common patterns across job sites
    const titlePatterns = [
      /<h1[^>]*>(.*?)<\/h1>/i,
      /<title[^>]*>(.*?)<\/title>/i,
      /<h2[^>]*class="[^"]*job[^"]*title[^"]*"[^>]*>(.*?)<\/h2>/i,
      /<h1[^>]*class="[^"]*title[^"]*"[^>]*>(.*?)<\/h1>/i,
    ];

    const companyPatterns = [
      /<span[^>]*class="[^"]*company[^"]*"[^>]*>(.*?)<\/span>/i,
      /<div[^>]*class="[^"]*company[^"]*"[^>]*>(.*?)<\/div>/i,
      /<a[^>]*class="[^"]*company[^"]*"[^>]*>(.*?)<\/a>/i,
      /<h2[^>]*class="[^"]*company[^"]*"[^>]*>(.*?)<\/h2>/i,
    ];

    const locationPatterns = [
      /<span[^>]*class="[^"]*location[^"]*"[^>]*>(.*?)<\/span>/i,
      /<div[^>]*class="[^"]*location[^"]*"[^>]*>(.*?)<\/div>/i,
      /location[^<]*<[^>]*>(.*?)<\/[^>]*>/i,
    ];

    const descriptionPatterns = [
      /<div[^>]*class="[^"]*description[^"]*"[^>]*>([\s\S]*?)<\/div>/i,
      /<div[^>]*class="[^"]*job-description[^"]*"[^>]*>([\s\S]*?)<\/div>/i,
      /<section[^>]*class="[^"]*description[^"]*"[^>]*>([\s\S]*?)<\/section>/i,
    ];

    // Try to extract with basic patterns
    for (const pattern of titlePatterns) {
      const match = html.match(pattern);
      if (match && match[1] && match[1].trim()) {
        title = cleanHtml(match[1]);
        break;
      }
    }

    for (const pattern of companyPatterns) {
      const match = html.match(pattern);
      if (match && match[1] && match[1].trim()) {
        company = cleanHtml(match[1]);
        break;
      }
    }

    for (const pattern of locationPatterns) {
      const match = html.match(pattern);
      if (match && match[1] && match[1].trim()) {
        location = cleanHtml(match[1]);
        break;
      }
    }

    for (const pattern of descriptionPatterns) {
      const match = html.match(pattern);
      if (match && match[1] && match[1].trim()) {
        description = cleanHtml(match[1]);
        break;
      }
    }

    // If basic parsing didn't work well, use OpenAI to extract information
    if (title === 'Unknown Position' || company === 'Unknown Company' || description === 'No description available' || description.length < 50) {
      // Determine the job site type
      const hostname = new URL(url).hostname.toLowerCase();
      let siteContext = '';
      
      if (hostname.includes('indeed')) {
        siteContext = 'This is an Indeed job posting. Look for job titles in h1 tags, company names in data-testid="inlineHeader-companyName" or similar, and descriptions in div with id="jobDescriptionText".';
      } else if (hostname.includes('glassdoor')) {
        siteContext = 'This is a Glassdoor job posting. Look for job titles in h1 tags, company names in employer info sections, and descriptions in job description containers.';
      } else if (hostname.includes('lever')) {
        siteContext = 'This is a Lever job posting. Look for job titles, company names, and descriptions in their specific markup structure.';
      } else if (hostname.includes('greenhouse')) {
        siteContext = 'This is a Greenhouse job posting. Look for job titles, company names, and descriptions in their application page structure.';
      } else {
        siteContext = 'This is a job posting from a company website or job board. Look for common job posting elements like job titles, company names, locations, and detailed job descriptions.';
      }

      const payload = {
        model: "gpt-4o-mini",
        messages: [
          {
            role: 'system',
            content: `You are a helpful assistant that extracts job information from HTML. ${siteContext} Extract the job title, company name, location, and full job description. Be thorough in finding the complete job description.`,
          },
          {
            role: 'user',
            content: `Extract the job title, company name, work location, and complete job description from this job posting HTML.

URL: ${url}

Please look for:
- Job title: Usually in h1 tags or prominent headings
- Company name: Often near the job title or in company information sections
- Location: Look for city, state, country, or "Remote" indicators
- Description: The full job description including responsibilities, requirements, benefits, etc.

Return the information in JSON format with keys: title, company, location, description.

HTML: ${html.substring(0, 20000)}...`,
          },
        ],
        response_format: { type: "json_object" },
      };

      const aiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.OPENAI_API_KEY!}`,
        },
        method: 'POST',
        body: JSON.stringify(payload),
      });

      if (!aiResponse.ok) {
        throw new HttpError(aiResponse.status, `Failed to parse job data with AI: ${aiResponse.statusText}`);
      }

      const aiJson = await aiResponse.json() as OpenAIResponse;
      if (!aiJson.choices || !aiJson.choices[0] || !aiJson.choices[0].message) {
        throw new HttpError(500, 'Invalid response format from OpenAI');
      }

      try {
        const parsedData = JSON.parse(aiJson.choices[0].message.content);
        return {
          title: parsedData.title || title,
          company: parsedData.company || company,
          location: parsedData.location || location,
          description: parsedData.description || description,
        };
      } catch (parseError) {
        // If AI response can't be parsed, return what we found with basic parsing
        console.error('Failed to parse AI response:', parseError);
      }
    }

    return {
      title,
      company,
      location,
      description,
    };
  } catch (error: any) {
    console.error('Error importing job from URL:', error);
    throw new HttpError(error.statusCode || 500, error.message || 'Failed to import job from URL. Please try a different URL or check if the page is accessible.');
  }
};

// Job recommendation types
type JobRecommendation = {
  id: string;
  title: string;
  company: string;
  location: string;
  salary?: string;
  type: 'full-time' | 'part-time' | 'contract' | 'internship';
  posted: string;
  url?: string;
  description: string;
  tags: string[];
  source: string;
  isRecommended?: boolean;
  matchScore?: number;
  applicants?: number;
};

// Helper function to extract skills from user's existing jobs
function extractUserSkills(jobs: any[]): string[] {
  const skillKeywords = ['react', 'typescript', 'javascript', 'python', 'java', 'node', 'aws', 'docker', 'kubernetes', 'sql', 'nosql', 'machine learning', 'ai', 'frontend', 'backend', 'fullstack', 'devops', 'mobile', 'ios', 'android', 'vue', 'angular', 'express', 'django', 'flask', 'spring', 'mongodb', 'postgresql', 'redis', 'graphql', 'rest', 'api'];
  
  const extractedSkills = new Set<string>();
  
  jobs.forEach(job => {
    const text = `${job.title} ${job.description}`.toLowerCase();
    skillKeywords.forEach(skill => {
      if (text.includes(skill)) {
        extractedSkills.add(skill);
      }
    });
  });
  
  return Array.from(extractedSkills);
}

// Helper function to determine user's preferred locations
function extractUserLocations(jobs: any[]): string[] {
  const locations = jobs.map(job => job.location).filter(Boolean);
  return [...new Set(locations)];
}

// Adzuna API integration (Free tier: 1000 calls/month)
async function fetchFromAdzuna(skills: string[], locations: string[]): Promise<JobRecommendation[]> {
  if (!process.env.ADZUNA_APP_ID || !process.env.ADZUNA_API_KEY) {
    console.log('Adzuna API credentials not configured, skipping...');
    return [];
  }

  try {
    const skillQuery = skills.slice(0, 3).join(' OR '); // Limit to top 3 skills
    const location = locations[0] || 'San Francisco';
    
    const url = `https://api.adzuna.com/v1/api/jobs/us/search/1?` +
      `app_id=${process.env.ADZUNA_APP_ID}&` +
      `app_key=${process.env.ADZUNA_API_KEY}&` +
      `results_per_page=10&` +
      `what=${encodeURIComponent(skillQuery)}&` +
      `where=${encodeURIComponent(location)}&` +
      `content-type=application/json`;

    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Adzuna API error: ${response.status}`);
    }

    const data = await response.json() as any;
    
    return data.results?.map((job: any, index: number) => ({
      id: `adzuna-${job.id || index}`,
      title: job.title || 'Unknown Position',
      company: job.company?.display_name || 'Unknown Company',
      location: job.location?.display_name || location,
      salary: job.salary_min && job.salary_max ? 
        `$${Math.round(job.salary_min/1000)}k - $${Math.round(job.salary_max/1000)}k` : 
        undefined,
      type: job.contract_type === 'permanent' ? 'full-time' : 
            job.contract_type === 'contract' ? 'contract' : 'full-time',
      posted: job.created ? new Date(job.created).toLocaleDateString() : 'Recently',
      url: job.redirect_url,
      description: job.description?.replace(/<[^>]*>/g, '').substring(0, 200) + '...' || 'No description available',
      tags: skills.slice(0, 4),
      source: 'Adzuna',
      isRecommended: true,
      matchScore: Math.max(85, Math.floor(Math.random() * 15) + 85), // 85-100% for recommended
    })) || [];
  } catch (error) {
    console.error('Error fetching from Adzuna:', error);
    return [];
  }
}

// The Muse API integration (Free tier available)
async function fetchFromTheMuse(): Promise<JobRecommendation[]> {
  try {
    console.log('🎭 Fetching jobs from The Muse API...');
    // Use broader search - remove restrictive category filters since they return 0 results
    // Just get recent jobs from all categories
    const url = 'https://www.themuse.com/api/public/jobs?' +
      'page=0&' +
      'descending=true&' +
      'items_per_page=15';

    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`The Muse API error: ${response.status}`);
    }

    const data = await response.json() as any;
    console.log(`🎭 The Muse API returned ${data.results?.length || 0} jobs from ${data.total || 0} total`);
    
    const jobs = data.results?.slice(0, 8).map((job: any, index: number) => ({
      id: `muse-${job.id || index}`,
      title: job.name || 'Unknown Position',
      company: job.company?.name || 'Unknown Company',
      location: job.locations?.[0]?.name || 'Remote',
      type: 'full-time' as const,
      posted: job.publication_date ? new Date(job.publication_date).toLocaleDateString() : 'Recently',
      url: job.refs?.landing_page,
      description: job.contents?.replace(/<[^>]*>/g, '').substring(0, 300) + '...' || 'No description available',
      tags: ['Professional', 'Career', 'Full-time'],
      source: 'The Muse',
      isRecommended: false,
    })) || [];
    
    console.log(`🎭 Processed ${jobs.length} jobs from The Muse`);
    return jobs;
  } catch (error) {
    console.error('Error fetching from The Muse:', error);
    return [];
  }
}

// Remotive API integration (Free, remote jobs focus)
async function fetchFromRemotive(): Promise<JobRecommendation[]> {
  try {
    const url = 'https://remotive.io/api/remote-jobs?category=software-dev&limit=10';

    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Remotive API error: ${response.status}`);
    }

    const data = await response.json() as any;
    
    return data.jobs?.map((job: any, index: number) => ({
      id: `remotive-${job.id || index}`,
      title: job.title || 'Unknown Position',
      company: job.company_name || 'Unknown Company',
      location: 'Remote',
      salary: job.salary || undefined,
      type: job.job_type === 'full_time' ? 'full-time' : 
            job.job_type === 'contract' ? 'contract' : 'full-time',
      posted: job.publication_date ? new Date(job.publication_date).toLocaleDateString() : 'Recently',
      url: job.url,
      description: job.description?.substring(0, 200) + '...' || 'No description available',
      tags: job.tags || ['Remote', 'Software'],
      source: 'Remotive',
      isRecommended: false,
    })) || [];
  } catch (error) {
    console.error('Error fetching from Remotive:', error);
    return [];
  }
}

// Enhanced mock data as fallback
function getFallbackRecommendedJobs(userSkills: string[]): JobRecommendation[] {
  const baseJobs = [
    {
      id: 'rec-1',
      title: 'Senior Software Engineer',
      company: 'TechCorp Inc.',
      location: 'San Francisco, CA',
      salary: '$140k - $180k',
      type: 'full-time' as const,
      posted: '2 days ago',
      url: 'https://example.com/job/1',
      description: 'Lead development of scalable web applications using modern technologies. Work with a talented team to build products that impact millions of users.',
      tags: userSkills.length > 0 ? userSkills.slice(0, 4) : ['React', 'TypeScript', 'AWS', 'GraphQL'],
      source: 'CareerDart',
      isRecommended: true,
      matchScore: 95,
      applicants: 23,
    },
    {
      id: 'rec-2',
      title: 'Full Stack Developer',
      company: 'StartupXYZ',
      location: 'Remote',
      salary: '$110k - $150k',
      type: 'full-time' as const,
      posted: '1 day ago',
      url: 'https://example.com/job/2',
      description: 'Join our fast-growing fintech startup to build the next generation of financial tools. Work with cutting-edge technologies.',
      tags: userSkills.length > 0 ? userSkills.slice(0, 4) : ['Node.js', 'React', 'PostgreSQL', 'Docker'],
      source: 'CareerDart',
      isRecommended: true,
      matchScore: 88,
      applicants: 15,
    },
    {
      id: 'rec-3',
      title: 'Product Manager',
      company: 'Innovation Labs',
      location: 'New York, NY',
      salary: '$120k - $160k',
      type: 'full-time' as const,
      posted: '3 days ago',
      url: 'https://example.com/job/3',
      description: 'Drive product strategy for our AI-powered platform. Collaborate with engineering and design teams to deliver exceptional experiences.',
      tags: ['Product Strategy', 'Analytics', 'Agile', 'AI/ML'],
      source: 'CareerDart',
      isRecommended: true,
      matchScore: 82,
      applicants: 42,
    },
  ];

  return baseJobs;
}

function getFallbackAvailableJobs(): JobRecommendation[] {
  return [
    {
      id: 'avail-1',
      title: 'Backend Engineer',
      company: 'DataCorp',
      location: 'Austin, TX',
      salary: '$100k - $130k',
      type: 'full-time' as const,
      posted: '1 week ago',
      description: 'Build robust backend systems handling petabytes of data. Work with microservices and cloud infrastructure.',
      tags: ['Python', 'Kubernetes', 'GCP', 'Microservices'],
      source: 'CareerDart',
      applicants: 67,
    },
    {
      id: 'avail-2',
      title: 'Frontend Developer',
      company: 'WebFlow Inc.',
      location: 'Remote',
      salary: '$90k - $120k',
      type: 'full-time' as const,
      posted: '5 days ago',
      description: 'Develop responsive web applications using modern JavaScript frameworks. Focus on performance and UX.',
      tags: ['React', 'Next.js', 'CSS', 'Performance'],
      source: 'CareerDart',
      applicants: 156,
    },
    {
      id: 'avail-3',
      title: 'DevOps Engineer',
      company: 'CloudTech',
      location: 'Denver, CO',
      salary: '$105k - $140k',
      type: 'full-time' as const,
      posted: '1 week ago',
      description: 'Manage cloud infrastructure and implement CI/CD pipelines. Work with AWS and monitoring systems.',
      tags: ['AWS', 'Terraform', 'Jenkins', 'Monitoring'],
      source: 'CareerDart',
      applicants: 28,
    },
  ];
}

// Main action for fetching recommended jobs
export const fetchRecommendedJobs = async (args: any, context: any): Promise<JobRecommendation[]> => {
  if (!context.user) {
    throw new HttpError(401, 'Authentication required');
  }

  try {
    // Get user's existing jobs to analyze preferences - with error handling
    let userJobs = [];
    try {
      if (context.entities && context.entities.Job) {
        userJobs = await context.entities.Job.findMany({
          where: { userId: context.user.id }
        });
      }
    } catch (dbError) {
      console.log('Could not access user jobs for recommendations, using generic approach');
      userJobs = [];
    }

    const userSkills = extractUserSkills(userJobs);
    const userLocations = extractUserLocations(userJobs);

    console.log(`Fetching recommendations for user ${context.user.id} with skills:`, userSkills);

    // Try to fetch from external APIs
    const [adzunaJobs] = await Promise.all([
      fetchFromAdzuna(userSkills, userLocations),
    ]);

    // Combine results
    let recommendedJobs = [...adzunaJobs];

    // If we don't have enough jobs from external APIs, add fallback data
    if (recommendedJobs.length < 3) {
      const fallbackJobs = getFallbackRecommendedJobs(userSkills);
      recommendedJobs = [...recommendedJobs, ...fallbackJobs];
    }

    // Limit to top 6 recommendations
    return recommendedJobs.slice(0, 6);

  } catch (error) {
    console.error('Error fetching recommended jobs:', error);
    
    // Return fallback data on error - always provide some recommendations
    return getFallbackRecommendedJobs([]);
  }
};

// Main action for fetching available jobs
export const fetchAvailableJobs = async (args: any, context: any): Promise<JobRecommendation[]> => {
  if (!context.user) {
    throw new HttpError(401, 'Authentication required');
  }

  try {
    console.log(`Fetching available jobs for user ${context.user.id}`);

    // Try to fetch from external APIs
    const [museJobs, remotiveJobs] = await Promise.all([
      fetchFromTheMuse(),
      fetchFromRemotive(),
    ]);

    console.log(`API Results - Muse: ${museJobs.length} jobs, Remotive: ${remotiveJobs.length} jobs`);

    // Combine results
    let availableJobs = [...museJobs, ...remotiveJobs];

    // If we don't have ANY jobs from external APIs, add fallback data
    if (availableJobs.length === 0) {
      console.log('No external API data available, using complete fallback');
      const fallbackJobs = getFallbackAvailableJobs();
      availableJobs = fallbackJobs;
    } else {
      console.log(`Using ${availableJobs.length} real jobs from external APIs`);
      // Only add a few fallback jobs if we have some real data but not enough
      if (availableJobs.length < 3) {
        console.log('Adding a few fallback jobs to supplement real data');
        const fallbackJobs = getFallbackAvailableJobs().slice(0, 3);
        availableJobs = [...availableJobs, ...fallbackJobs];
      }
    }

    // Limit to top 8 available jobs
    const finalJobs = availableJobs.slice(0, 8);
    console.log(`Returning ${finalJobs.length} total jobs (real + fallback as needed)`);
    
    return finalJobs;

  } catch (error) {
    console.error('Error fetching available jobs:', error);
    
    // Return fallback data on error
    return getFallbackAvailableJobs();
  }
};

// Inbox Actions
export const markMessageAsRead: MarkMessageAsRead<{ messageId: string }, InboxMessage> = async (
  { messageId },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  const message = await context.entities.InboxMessage.findFirst({
    where: {
      id: messageId,
      userId: context.user.id,
    },
  });

  if (!message) {
    throw new HttpError(404, 'Message not found');
  }

  return context.entities.InboxMessage.update({
    where: { id: messageId },
    data: {
      isRead: true,
      openedAt: message.openedAt || new Date(),
    },
  });
};

export const markMessageAsStarred: MarkMessageAsStarred<{ messageId: string; starred: boolean }, InboxMessage> = async (
  { messageId, starred },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  const message = await context.entities.InboxMessage.findFirst({
    where: {
      id: messageId,
      userId: context.user.id,
    },
  });

  if (!message) {
    throw new HttpError(404, 'Message not found');
  }

  return context.entities.InboxMessage.update({
    where: { id: messageId },
    data: { isStarred: starred },
  });
};

export const archiveMessage: ArchiveMessage<{ messageId: string }, InboxMessage> = async (
  { messageId },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  const message = await context.entities.InboxMessage.findFirst({
    where: {
      id: messageId,
      userId: context.user.id,
    },
  });

  if (!message) {
    throw new HttpError(404, 'Message not found');
  }

  return context.entities.InboxMessage.update({
    where: { id: messageId },
    data: { isArchived: true },
  });
};

export const deleteMessage: DeleteMessage<{ messageId: string }, { success: boolean }> = async (
  { messageId },
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  const message = await context.entities.InboxMessage.findFirst({
    where: {
      id: messageId,
      userId: context.user.id,
    },
  });

  if (!message) {
    throw new HttpError(404, 'Message not found');
  }

  await context.entities.InboxMessage.delete({
    where: { id: messageId },
  });

  return { success: true };
};

export const sendInboxMessage: SendInboxMessage<{
  userId: number;
  title: string;
  content: string;
  type: string;
  category?: string;
  priority?: string;
  actions?: any[];
  relatedFeedbackId?: string;
  senderName?: string;
  senderEmail?: string;
}, InboxMessage> = async (args, context) => {
  // This action should typically be called by admin users or system processes
  // For now, we'll allow any authenticated user, but in production you'd want admin checks

  const {
    userId,
    title,
    content,
    type,
    category = 'general',
    priority = 'normal',
    actions,
    relatedFeedbackId,
    senderName = 'CareerDart Team',
    senderEmail = '<EMAIL>'
  } = args;

  return context.entities.InboxMessage.create({
    data: {
      userId,
      title,
      content,
      type,
      category,
      priority,
      actions,
      relatedFeedbackId,
      senderName,
      senderEmail,
      deliveredAt: new Date(),
    },
  });
};

export const sendBroadcastMessage: SendBroadcastMessage<{
  title: string;
  content: string;
  type: string;
  category?: string;
  priority?: string;
  actions?: any[];
  userFilter?: 'all' | 'paid' | 'free' | 'new';
  senderName?: string;
  senderEmail?: string;
}, { success: boolean; messagesSent: number }> = async (args, context) => {
  // This should be restricted to admin users in production
  if (!context.user) {
    throw new HttpError(401);
  }

  const {
    title,
    content,
    type,
    category = 'general',
    priority = 'normal',
    actions,
    userFilter = 'all',
    senderName = 'CareerDart Team',
    senderEmail = '<EMAIL>'
  } = args;

  // Get users based on filter
  let whereClause: any = {};

  if (userFilter === 'paid') {
    whereClause.hasPaid = true;
  } else if (userFilter === 'free') {
    whereClause.hasPaid = false;
  } else if (userFilter === 'new') {
    // Users created in the last 7 days
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    whereClause.createdAt = { gte: sevenDaysAgo };
  }

  const users = await context.entities.User.findMany({
    where: whereClause,
    select: { id: true },
  });

  // Create messages for all users
  const messagePromises = users.map(user =>
    context.entities.InboxMessage.create({
      data: {
        userId: user.id,
        title,
        content,
        type,
        category,
        priority,
        actions,
        senderName,
        senderEmail,
        deliveredAt: new Date(),
      },
    })
  );

  await Promise.all(messagePromises);

  return {
    success: true,
    messagesSent: users.length,
  };
};